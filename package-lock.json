{"name": "fuzzing-platform", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "fuzzing-platform", "version": "0.0.0", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@jiaminghi/data-view": "^2.10.0", "@kjgl77/datav-vue3": "^1.7.3", "aos": "^2.3.4", "axios": "^1.7.7", "element-plus": "^2.8.0", "font-awesome": "^4.7.0", "gsap": "^3.12.5", "js-cookie": "^3.0.5", "qs": "^6.13.0", "tsparticles": "^3.7.1", "tsparticles-engine": "^2.12.0", "vue": "^3.4.35", "vue-router": "^4.4.3", "vuex": "^4.1.0"}, "devDependencies": {"@types/qs": "^6.9.17", "@vitejs/plugin-vue": "^5.1.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "sass-embedded": "^1.77.8", "tailwindcss": "^3.4.17", "vite": "^5.4.0"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "integrity": "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/helper-string-parser": {"version": "7.24.8", "resolved": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.24.8.tgz", "integrity": "sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.24.7", "resolved": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz", "integrity": "sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.25.6", "resolved": "https://registry.npmmirror.com/@babel/parser/-/parser-7.25.6.tgz", "integrity": "sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==", "dependencies": {"@babel/types": "^7.25.6"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.25.6", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.25.6.tgz", "integrity": "sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.25.6", "resolved": "https://registry.npmmirror.com/@babel/types/-/types-7.25.6.tgz", "integrity": "sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==", "dependencies": {"@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bufbuild/protobuf": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/@bufbuild/protobuf/-/protobuf-1.10.0.tgz", "integrity": "sha512-QDdVFLoN93Zjg36NoQPZfsVH9tZew7wKDKyV5qRdj8ntT4wQCOradQjRaTdwMhWUYsgKsvCINKKm87FdEk96Ag==", "dev": true}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "resolved": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==", "engines": {"node": ">=10"}}, "node_modules/@element-plus/icons-vue": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz", "integrity": "sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==", "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz", "integrity": "sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["aix"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.21.5.tgz", "integrity": "sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz", "integrity": "sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.21.5.tgz", "integrity": "sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz", "integrity": "sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz", "integrity": "sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz", "integrity": "sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz", "integrity": "sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz", "integrity": "sha512-bPb5<PERSON><PERSON>ZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz", "integrity": "sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz", "integrity": "sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz", "integrity": "sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==", "cpu": ["loong64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz", "integrity": "sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==", "cpu": ["mips64el"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz", "integrity": "sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz", "integrity": "sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz", "integrity": "sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==", "cpu": ["s390x"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz", "integrity": "sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz", "integrity": "sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz", "integrity": "sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz", "integrity": "sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz", "integrity": "sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz", "integrity": "sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-x64": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz", "integrity": "sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@floating-ui/core": {"version": "1.6.7", "resolved": "https://registry.npmmirror.com/@floating-ui/core/-/core-1.6.7.tgz", "integrity": "sha512-yDzVT/Lm101nQ5TCVeK65LtdN7Tj4Qpr9RTXJ2vPFLqtLxwOrpoxAHAJI8J3yYWUc40J0BDBheaitK5SJmno2g==", "dependencies": {"@floating-ui/utils": "^0.2.7"}}, "node_modules/@floating-ui/dom": {"version": "1.6.10", "resolved": "https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.6.10.tgz", "integrity": "sha512-fskgCFv8J8OamCmyun8MfjB1Olfn+uZKjOKZ0vhYF3gRmEUXcGOjxWL8bBr7i4kIuPZ2KD2S3EUIOxnjC8kl2A==", "dependencies": {"@floating-ui/core": "^1.6.0", "@floating-ui/utils": "^0.2.7"}}, "node_modules/@floating-ui/utils": {"version": "0.2.7", "resolved": "https://registry.npmmirror.com/@floating-ui/utils/-/utils-0.2.7.tgz", "integrity": "sha512-X8R8Oj771YRl/w+c1HqAC1szL8zWQRwFvgDwT129k9ACdBoud/+/rX9V0qiMl6LWUdP9voC2nDVZYPMQQsb6eA=="}, "node_modules/@fortawesome/fontawesome-common-types": {"version": "6.7.2", "resolved": "https://registry.npmmirror.com/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.7.2.tgz", "integrity": "sha512-Zs+YeHUC5fkt7Mg1l6XTniei3k4bwG/yo3iFUtZWd/pMx9g3fdvkSK9E0FOC+++phXOka78uJcYb8JaFkW52Xg==", "engines": {"node": ">=6"}}, "node_modules/@fortawesome/fontawesome-free": {"version": "6.7.2", "resolved": "https://registry.npmmirror.com/@fortawesome/fontawesome-free/-/fontawesome-free-6.7.2.tgz", "integrity": "sha512-JUOtgFW6k9u4Y+xeIaEiLr3+cjoUPiAuLXoyKOJSia6Duzb7pq+A76P9ZdPDoAoxHdHzq6gE9/jKBGXlZT8FbA==", "engines": {"node": ">=6"}}, "node_modules/@fortawesome/fontawesome-svg-core": {"version": "6.7.2", "resolved": "https://registry.npmmirror.com/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.7.2.tgz", "integrity": "sha512-yxtOBWDrdi5DD5o1pmVdq3WMCvnobT0LU6R8RyyVXPvFRd2o79/0NCuQoCjNTeZz9EzA9xS3JxNWfv54RIHFEA==", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-solid-svg-icons": {"version": "6.7.2", "resolved": "https://registry.npmmirror.com/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.7.2.tgz", "integrity": "sha512-GsBrnOzU8uj0LECDfD5zomZJIjrPhIlWU82AHwa2s40FKH+kcxQaBvBo3Z4TxyZHIyX8XTDxsyA33/Vx9eFuQA==", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/vue-fontawesome": {"version": "3.0.8", "resolved": "https://registry.npmmirror.com/@fortawesome/vue-fontawesome/-/vue-fontawesome-3.0.8.tgz", "integrity": "sha512-yyHHAj4G8pQIDfaIsMvQpwKMboIZtcHTUvPqXjOHyldh1O1vZfH4W03VDPv5RvI9P6DLTzJQlmVgj9wCf7c2Fw==", "peerDependencies": {"@fortawesome/fontawesome-svg-core": "~1 || ~6", "vue": ">= 3.0.0 < 4"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "https://registry.npmmirror.com/@isaacs/cliui/-/cliui-8.0.2.tgz", "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==", "dev": true, "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@jiaminghi/bezier-curve": {"version": "0.0.9", "resolved": "https://registry.npmmirror.com/@jiaminghi/bezier-curve/-/bezier-curve-0.0.9.tgz", "integrity": "sha512-u9xJPOEl6Dri2E9FfmJoGxYQY7vYJkURNX04Vj64tdi535tPrpkuf9Sm0lNr3QTKdHQh0DdNRsaa62FLQNQEEw==", "dependencies": {"@babel/runtime": "^7.5.5"}}, "node_modules/@jiaminghi/c-render": {"version": "0.4.3", "resolved": "https://registry.npmmirror.com/@jiaminghi/c-render/-/c-render-0.4.3.tgz", "integrity": "sha512-FJfzj5hGj7MLqqqI2D7vEzHKbQ1Ynnn7PJKgzsjXaZpJzTqs2Yw5OSeZnm6l7Qj7jyPAP53lFvEQNH4o4j6s+Q==", "dependencies": {"@babel/runtime": "^7.5.5", "@jiaminghi/bezier-curve": "*", "@jiaminghi/color": "*", "@jiaminghi/transition": "*"}}, "node_modules/@jiaminghi/charts": {"version": "0.2.18", "resolved": "https://registry.npmmirror.com/@jiaminghi/charts/-/charts-0.2.18.tgz", "integrity": "sha512-K+HXaOOeWG9OOY1VG6M4mBreeeIAPhb9X+khG651AbnwEwL6G2UtcAQ8GWCq6GzhczcLwwhIhuaHqRygwHC0sA==", "dependencies": {"@babel/runtime": "^7.5.5", "@jiaminghi/c-render": "^0.4.3"}}, "node_modules/@jiaminghi/color": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/@jiaminghi/color/-/color-1.1.3.tgz", "integrity": "sha512-ZY3hdorgODk4OSTbxyXBPxAxHPIVf9rPlKJyK1C1db46a50J0reFKpAvfZG8zMG3lvM60IR7Qawgcu4ZDO3+Hg=="}, "node_modules/@jiaminghi/data-view": {"version": "2.10.0", "resolved": "https://registry.npmmirror.com/@jiaminghi/data-view/-/data-view-2.10.0.tgz", "integrity": "sha512-Cud2MTiMcqc5k2KWabR/svuVQmXHANqURo+yj40370/LdI/gyUJ6LG203hWXEnT1nMCeiv/SLVmxv3PXLScCeA==", "dependencies": {"@babel/runtime": "^7.5.5", "@jiaminghi/charts": "*"}}, "node_modules/@jiaminghi/transition": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/@jiaminghi/transition/-/transition-1.1.11.tgz", "integrity": "sha512-owBggipoHMikDHHDW5Gc7RZYlVuvxHADiU4bxfjBVkHDAmmck+fCkm46n2JzC3j33hWvP9nSCAeh37t6stgWeg==", "dependencies": {"@babel/runtime": "^7.5.5"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@kjgl77/datav-vue3": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/@kjgl77/datav-vue3/-/datav-vue3-1.7.3.tgz", "integrity": "sha512-8zNu2zDXylDS6aL8SNDR7/h4ccO2LnBg/KbTwOeF7p9hqiRW9TR7Zd/e8baYnJPb9tAwYOubzWTQnjUTdtE8dA==", "dependencies": {"@jiaminghi/c-render": "^0.4.3", "@jiaminghi/charts": "^0.2.18", "@jiaminghi/color": "^1.1.3", "@vueuse/core": "^10.9.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "https://registry.npmmirror.com/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==", "dev": true, "optional": true, "engines": {"node": ">=14"}}, "node_modules/@popperjs/core": {"name": "@sxzz/popperjs-es", "version": "2.11.7", "resolved": "https://registry.npmmirror.com/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz", "integrity": "sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ=="}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.21.2.tgz", "integrity": "sha512-fSuPrt0ZO8uXeS+xP3b+yYTCBUd05MoSp2N/MFOgjhhUhMmchXlpTQrTpI8T+YAwAQuK7MafsCOxW7VrPMrJcg==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.2.tgz", "integrity": "sha512-xGU5ZQmPlsjQS6tzTTGwMsnKUtu0WVbl0hYpTPauvbRAnmIvpInhJtgjj3mcuJpEiuUw4v1s4BimkdfDWlh7gA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.21.2.tgz", "integrity": "sha512-99AhQ3/ZMxU7jw34Sq8brzXqWH/bMnf7ZVhvLk9QU2cOepbQSVTns6qoErJmSiAvU3InRqC2RRZ5ovh1KN0d0Q==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.21.2.tgz", "integrity": "sha512-ZbRaUvw2iN/y37x6dY50D8m2BnDbBjlnMPotDi/qITMJ4sIxNY33HArjikDyakhSv0+ybdUxhWxE6kTI4oX26w==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.21.2.tgz", "integrity": "sha512-ztRJJMiE8nnU1YFcdbd9BcH6bGWG1z+jP+IPW2oDUAPxPjo9dverIOyXz76m6IPA6udEL12reYeLojzW2cYL7w==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.21.2.tgz", "integrity": "sha512-flOcGHDZajGKYpLV0JNc0VFH361M7rnV1ee+NTeC/BQQ1/0pllYcFmxpagltANYt8FYf9+kL6RSk80Ziwyhr7w==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.21.2.tgz", "integrity": "sha512-69CF19Kp3TdMopyteO/LJbWufOzqqXzkrv4L2sP8kfMaAQ6iwky7NoXTp7bD6/irKgknDKM0P9E/1l5XxVQAhw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.21.2.tgz", "integrity": "sha512-48pD/fJkTiHAZTnZwR0VzHrao70/4MlzJrq0ZsILjLW/Ab/1XlVUStYyGt7tdyIiVSlGZbnliqmult/QGA2O2w==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.21.2.tgz", "integrity": "sha512-cZdyuInj0ofc7mAQpKcPR2a2iu4YM4FQfuUzCVA2u4HI95lCwzjoPtdWjdpDKyHxI0UO82bLDoOaLfpZ/wviyQ==", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.21.2.tgz", "integrity": "sha512-RL56JMT6NwQ0lXIQmMIWr1SW28z4E4pOhRRNqwWZeXpRlykRIlEpSWdsgNWJbYBEWD84eocjSGDu/XxbYeCmwg==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.21.2.tgz", "integrity": "sha512-PMxkrWS9z38bCr3rWvDFVGD6sFeZJw4iQlhrup7ReGmfn7Oukrr/zweLhYX6v2/8J6Cep9IEA/SmjXjCmSbrMQ==", "cpu": ["s390x"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.21.2.tgz", "integrity": "sha512-B90tYAUoLhU22olrafY3JQCFLnT3NglazdwkHyxNDYF/zAxJt5fJUB/yBoWFoIQ7SQj+KLe3iL4BhOMa9fzgpw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.21.2.tgz", "integrity": "sha512-7twFizNXudESmC9oneLGIUmoHiiLppz/Xs5uJQ4ShvE6234K0VB1/aJYU3f/4g7PhssLGKBVCC37uRkkOi8wjg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.2.tgz", "integrity": "sha512-9rRero0E7qTeYf6+rFh3AErTNU1VCQg2mn7CQcI44vNUWM9Ze7MSRS/9RFuSsox+vstRt97+x3sOhEey024FRQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.21.2.tgz", "integrity": "sha512-5rA4vjlqgrpbFVVHX3qkrCo/fZTj1q0Xxpg+Z7yIo3J2AilW7t2+n6Q8Jrx+4MrYpAnjttTYF8rr7bP46BPzRw==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.21.2.tgz", "integrity": "sha512-6UUxd0+SKomjdzuAcp+HAmxw1FlGBnl1v2yEPSabtx4lBfdXHDVsW7+lQkgz9cNFJGY3AWR7+V8P5BqkD9L9nA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@tsparticles/basic": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/basic/-/basic-3.7.1.tgz", "integrity": "sha512-oJMJ3qzYUROYaOEsaFXkVynxT2OTWBXbQ9MNc1bJi/bVc1VOU44VN7X/KmiZjD+w1U+Qalk6BeVvDRwpFshblw==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1", "@tsparticles/move-base": "3.7.1", "@tsparticles/plugin-hex-color": "3.7.1", "@tsparticles/plugin-hsl-color": "3.7.1", "@tsparticles/plugin-rgb-color": "3.7.1", "@tsparticles/shape-circle": "3.7.1", "@tsparticles/updater-color": "3.7.1", "@tsparticles/updater-opacity": "3.7.1", "@tsparticles/updater-out-modes": "3.7.1", "@tsparticles/updater-size": "3.7.1"}}, "node_modules/@tsparticles/engine": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/engine/-/engine-3.7.1.tgz", "integrity": "sha512-GYzBgq/oOE9YJdOL1++MoawWmYg4AvVct6CIrJGx84ZRb3U2owYmLsRGabYl0qX1CWWOvUG569043RJmyp/vQA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "hasInstallScript": true}, "node_modules/@tsparticles/interaction-external-attract": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-attract/-/interaction-external-attract-3.7.1.tgz", "integrity": "sha512-cpnMsFJ7ZJNKccpQvskKvSs1ofknByHE6FGqbEb17ij7HqvbECQOCOVKHPFnYipHe14cXor/Cd+nVisRcTASoQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-bounce": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-bounce/-/interaction-external-bounce-3.7.1.tgz", "integrity": "sha512-npvU9Qt6WDonjezHqi+hWM44ga2Oh5yXdr8eSoJpvuHZrCP7rIdRSz5XseHouO1bMS9DbXk86sx4qwrhB5w58w==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-bubble": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-bubble/-/interaction-external-bubble-3.7.1.tgz", "integrity": "sha512-WdbYL46lMfuf2g5kfVB1hhhxRBtEXDvnwz8PJwLKurSThL/27bqsqysyXsMzXtLByXUneGhtJTj4D5I5RYdgjA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-connect": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-connect/-/interaction-external-connect-3.7.1.tgz", "integrity": "sha512-hqx0ANIbjLIz/nxmk0LvqANBiNLLmVybbCA7N+xDHtEORvpKjNlKEvMz6Razocl6vRjoHZ/olSwcxIG84dh/cg==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-grab": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-grab/-/interaction-external-grab-3.7.1.tgz", "integrity": "sha512-JMYpFW+7YvkpK5MYlt4Ec3Gwb5ZxS7RLVL8IRUSd5yJOw25husPTYg+FQywxrt5WhKe+tPsCAYo+uGIbTTHi9w==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-pause": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-pause/-/interaction-external-pause-3.7.1.tgz", "integrity": "sha512-Kkp+7sCe24hawH0XvS1V6UCCuHfMvpLK7oseqSam9Gt4SyGrFvaqIXxkjXhRhn9MysJyKFPBV4/dtBM1HR9p6A==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-push": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-push/-/interaction-external-push-3.7.1.tgz", "integrity": "sha512-4VoaR5jvXgQdB7irtq4uSZYr5c+D6TBTVEnLVpBfJhUs6jhw6mgN5g7yp5izIYkK0AlcO431MHn8dvJacvRLDw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-remove": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-remove/-/interaction-external-remove-3.7.1.tgz", "integrity": "sha512-FRBW7U7zD5MkO6/b7e8iSMk/UTtRLY2XiIVFZNsKri3Re3yPpvZzzd5tl2YlYGQlg1Xc+K8SJYMQQA3PtgQ/Tg==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-repulse": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-repulse/-/interaction-external-repulse-3.7.1.tgz", "integrity": "sha512-mwM06dVmg2FEvHMQsPOfRBQWACbjf3qnelODkqI9DSVxQ0B8DESP4BYNXyraFGYv00YiPzRv5Xy/uejHdbsQUA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-slow": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-slow/-/interaction-external-slow-3.7.1.tgz", "integrity": "sha512-CfCAs3kUQC3pLOj0dbzn5AolQyBHgjxORLdfnYBhepvFV1BXB+4ytChRfXBzjykBPI6U+rCnw5Fk/vVjAroSFA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-external-trail": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-external-trail/-/interaction-external-trail-3.7.1.tgz", "integrity": "sha512-M7lNQUWP15m8YIDP/JZcZAXaVJLqdwpBs0Uv9F6dU6jsnNXwwHFVFZ+1icrnlbgl9k/Ehhodbdo5weE7GHhQhQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-particles-attract": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-particles-attract/-/interaction-particles-attract-3.7.1.tgz", "integrity": "sha512-UABbBORKaiItAT8vR0t4ye2H3VE6/Ah4zcylBlnq0Jd5yDkyP4rnkwhicaY6y4Zlfwyx+0PWdAC4f/ziFAyObg==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-particles-collisions": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-particles-collisions/-/interaction-particles-collisions-3.7.1.tgz", "integrity": "sha512-0GY9++Gn2KXViyeifXWkH7a2UO5+uRwyS1rDeTN8eleyiq2j9zQf4xztZEIft8T0hTetq2rkWxQ92j2kev6NVA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/interaction-particles-links": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/interaction-particles-links/-/interaction-particles-links-3.7.1.tgz", "integrity": "sha512-BxCXAAOBNmEvlyOQzwprryW8YdtMIop2v4kgSCff5MCtDwYWoQIfzaQlWbBAkD9ey6BoF8iMjhBUaY1MnDecTA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/move-base": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/move-base/-/move-base-3.7.1.tgz", "integrity": "sha512-LPtMHwJHhzwfRIcSAk814fY9NcRiICwaEbapaJSYyP1DwscSXqOWoyAEWwzV9hMgAcPdsED6nGeg8RCXGm58lw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/move-parallax": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/move-parallax/-/move-parallax-3.7.1.tgz", "integrity": "sha512-B40azo6EJyMdI+kmIxpqWDaObPwODTYLDCikzkZ73n5tS6OhFUlkz81Scfo+g1iGTdryKFygUKhVGcG1EFuA5g==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-absorbers": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-absorbers/-/plugin-absorbers-3.7.1.tgz", "integrity": "sha512-3s+fILLV1tdKOq/bXwfoxFVbzkWwXpdWTC2C0QIP6BFwDSQqV5txluiLEf7SCf8C5etQ6dstEnOgVbdzK7+eWA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-easing-quad": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-easing-quad/-/plugin-easing-quad-3.7.1.tgz", "integrity": "sha512-nSwKCRe6C/noCi3dyZlm1GiQGask0aXdWDuS36b82iwzwQ01cBTXeXR25mLr4fsfMLFfYAZXyBxEMMpw3rkSiw==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-emitters": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-emitters/-/plugin-emitters-3.7.1.tgz", "integrity": "sha512-WV5Uwxp/Ckqv5kZynTj6mj13jYbQCArNLFv8ks+zjdlteoyT5EhQl4rg+TalaySCb1zCd6Fu2Scp35l3JJgbnw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-emitters-shape-circle": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-emitters-shape-circle/-/plugin-emitters-shape-circle-3.7.1.tgz", "integrity": "sha512-eBwktnGROkiyCvtrSwdPpoRbIjQgV/Odq//0dw8D+qUdnox6dNzzhJjz8L2LAA2kQZBqtdBqV2kcx3w5ZdqoEQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1", "@tsparticles/plugin-emitters": "3.7.1"}}, "node_modules/@tsparticles/plugin-emitters-shape-square": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-emitters-shape-square/-/plugin-emitters-shape-square-3.7.1.tgz", "integrity": "sha512-nvGBsRLrkiz6Q38TJRl8Y/eu9i1ChQ9oorQydLBok+iZ6MefuOj39iYsAOkD1w9yRVrFWKHG6CR1mmJUniz/HA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1", "@tsparticles/plugin-emitters": "3.7.1"}}, "node_modules/@tsparticles/plugin-hex-color": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-hex-color/-/plugin-hex-color-3.7.1.tgz", "integrity": "sha512-7xu3MV8EdNNejjYyEmrq5fCDdYAcqz/9VatLpnwtwR5Q5t2qI0tD4CrcGaFfC/rTAVJacfiJe02UV/hlj03KKA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-hsl-color": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-hsl-color/-/plugin-hsl-color-3.7.1.tgz", "integrity": "sha512-zzAI1CuoCMBJhgeYZ5Rq42nGbPg35ZzIs11eQegjsWG5Msm5QKSj60qPzERnoUcCc4HCKtIWP7rYMz6h3xpoEg==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/plugin-rgb-color": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/plugin-rgb-color/-/plugin-rgb-color-3.7.1.tgz", "integrity": "sha512-taEraTpCYR6jpjflqBL95tN0zFU8JrAChXTt8mxVn7gddxoNMHI/LGymEPRCweLukwV6GQyAGOkeGEdWDPtYTA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-circle": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-circle/-/shape-circle-3.7.1.tgz", "integrity": "sha512-kmOWaUuFwuTtcCFYjuyJbdA5qDqWdGsharLalYnIczkLu2c1I8jJo/OmGePKhWn62ocu7mqKMomfElkIcw2AsA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-emoji": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-emoji/-/shape-emoji-3.7.1.tgz", "integrity": "sha512-mX18c/xhYVljS/r5Xbowzclw+1YwhtWoQFOOfkmjjZppA+RjgcwSKLvH6E20PaH1yVTjBOfSF+3POKpwsULzTg==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-image": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-image/-/shape-image-3.7.1.tgz", "integrity": "sha512-eDzfkQhqLY6fb9QH2Vo9TGfdJBFFpYnojhxQxc7IdzIwOFMD3JK4B52RVl9oowR+rNE8dNp6P2L+eMAF4yld0g==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-line": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-line/-/shape-line-3.7.1.tgz", "integrity": "sha512-lMPYApUpg7avxmYPfHHr4dQepZSNn/g0Q1/g2+lnTi8ZtUBiCZ2WMVy9R3GOzyozbnzigLQ6AJRnOpsUZV7H4g==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-polygon": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-polygon/-/shape-polygon-3.7.1.tgz", "integrity": "sha512-5FrRfpYC3qnvV2nXBLE4Q0v+SMNWJO8xgzh6MBFwfptvqH4EOrqc/58eS5x0jlf+evwf9LjPgeGkOTcwaHHcYQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-square": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-square/-/shape-square-3.7.1.tgz", "integrity": "sha512-7VCqbRwinjBZ+Ryme27rOtl+jKrET8qDthqZLrAoj3WONBqyt+R9q6SXAJ9WodqEX68IBvcluqbFY5qDZm8iAQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-star": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-star/-/shape-star-3.7.1.tgz", "integrity": "sha512-3G<PERSON>oipioyWKLEQYT11Sx3k6AObu3dbv/A5LRqGGTQm5IR6UACa+INwykZYI0a+MdJJMb83E0e4Fn3hlZbi0/8w==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/shape-text": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/shape-text/-/shape-text-3.7.1.tgz", "integrity": "sha512-aU1V9O8uQQBlL0jGFh9Q0b5vQ1Ji6Oo5ptyyj5yJ5uP/ZU00L0Vhk4DNyLXpaU0+H6OBoPpCqnvEsZBB9/HmCQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/slim": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/slim/-/slim-3.7.1.tgz", "integrity": "sha512-OtJEhud2KleX7OxiG2r/VYriHNIwTpFm3sPFy4EOJzAD0EW7KZoKXGpGn5gwGI1NWeB0jso92yNTrTC2ZTW0qw==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/basic": "3.7.1", "@tsparticles/engine": "3.7.1", "@tsparticles/interaction-external-attract": "3.7.1", "@tsparticles/interaction-external-bounce": "3.7.1", "@tsparticles/interaction-external-bubble": "3.7.1", "@tsparticles/interaction-external-connect": "3.7.1", "@tsparticles/interaction-external-grab": "3.7.1", "@tsparticles/interaction-external-pause": "3.7.1", "@tsparticles/interaction-external-push": "3.7.1", "@tsparticles/interaction-external-remove": "3.7.1", "@tsparticles/interaction-external-repulse": "3.7.1", "@tsparticles/interaction-external-slow": "3.7.1", "@tsparticles/interaction-particles-attract": "3.7.1", "@tsparticles/interaction-particles-collisions": "3.7.1", "@tsparticles/interaction-particles-links": "3.7.1", "@tsparticles/move-parallax": "3.7.1", "@tsparticles/plugin-easing-quad": "3.7.1", "@tsparticles/shape-emoji": "3.7.1", "@tsparticles/shape-image": "3.7.1", "@tsparticles/shape-line": "3.7.1", "@tsparticles/shape-polygon": "3.7.1", "@tsparticles/shape-square": "3.7.1", "@tsparticles/shape-star": "3.7.1", "@tsparticles/updater-life": "3.7.1", "@tsparticles/updater-rotate": "3.7.1", "@tsparticles/updater-stroke-color": "3.7.1"}}, "node_modules/@tsparticles/updater-color": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-color/-/updater-color-3.7.1.tgz", "integrity": "sha512-QimV3yn17dcdJx7PpTwLtw9BhkQ0q8qFF035OdcZpnynBPAO/hg0zvSMpMGoeuDVFH02wWBy4h2/BYCv6wh6Sw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-destroy": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-destroy/-/updater-destroy-3.7.1.tgz", "integrity": "sha512-krXNoMDKyeyE/ZjQh3LVjrLYivFefQOQ9i+B7RpMe7x4h+iRgpB6npTCqidGQ82+hZ8G6xfQ9ToduebWwK4JGg==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-life": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-life/-/updater-life-3.7.1.tgz", "integrity": "sha512-NY5gUrgO5AsARNC0usP9PKahXf7JCxbP/H1vzTfA0SJw4veANfWTldOvhIlcm2CHVP5P1b827p0hWsBHECwz7A==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-opacity": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-opacity/-/updater-opacity-3.7.1.tgz", "integrity": "sha512-YcyviCooTv7SAKw7sxd84CfJqZ7dYPSdYZzCpedV6TKIObRiwLqXlyLXQGJ3YltghKQSCSolmVy8woWBCDm1qA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-out-modes": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-out-modes/-/updater-out-modes-3.7.1.tgz", "integrity": "sha512-Cb5sWquRtUYLSiFpmBjjYKRdpNV52diCo9+qMtK1oVlldDBhUwqO+1TQjdlaA2yl5DURlY9ZfOHXvY+IT7CHCw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-roll": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-roll/-/updater-roll-3.7.1.tgz", "integrity": "sha512-gHLRqpTGVGPJBEAIPUiYVembIn5bcaTXXxsUJEM/IN+GIOvj2uZZGZ4r2aFTA6WugqEbJsJdblDSvMfouyz7Ug==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-rotate": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-rotate/-/updater-rotate-3.7.1.tgz", "integrity": "sha512-toVHwl+h6SvtA8dyxSA2kMH2QdDA71vehuAa+HoRqf1y06h5kxyYiMKZFHCqDJ6lFfRPs47MjrC9dD2bDz14MQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-size": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-size/-/updater-size-3.7.1.tgz", "integrity": "sha512-+Y0H0PnDJVIsJ+zHTyubYu1jtRFmVnY1dAv3VCjScIDw6bcpL/ol+HrtHTGIX0WbMyUfjCyALfAoaXi/Wm8VcQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-stroke-color": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-stroke-color/-/updater-stroke-color-3.7.1.tgz", "integrity": "sha512-VHhQkCNuxjx/Hy7A+g0Yijb24T0+wQ3jNsF/yfrR9dEdZWSBiimZLvV1bilPdAeEtieAJTAZo2VNhcD1snF0iQ==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-tilt": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-tilt/-/updater-tilt-3.7.1.tgz", "integrity": "sha512-pSOXoXPre1VPKC5nC5GW0L9jw63w1dVdsDdggEau7MP9xO7trko9L/KyayBX12Y4Ief1ca12Incxxr67hw7GGA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-twinkle": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-twinkle/-/updater-twinkle-3.7.1.tgz", "integrity": "sha512-maRTqPbeZcxBK6s1ry+ih71qSVaitfP1KTrAKR38v26GMwyO6z+zYV2bu9WTRt21FRFAoxlMLWxNu21GtQoXDA==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@tsparticles/updater-wobble": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/@tsparticles/updater-wobble/-/updater-wobble-3.7.1.tgz", "integrity": "sha512-YIlNg4L0w4egQJhPLpgcvcfv9+X621+cQsrdN9sSmajxhhwtEQvQUvFUzGTcvpjVi+GcBNp0t4sCKEzoP8iaYw==", "dependencies": {"@tsparticles/engine": "3.7.1"}}, "node_modules/@types/estree": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.5.tgz", "integrity": "sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==", "dev": true}, "node_modules/@types/lodash": {"version": "4.17.7", "resolved": "https://registry.npmmirror.com/@types/lodash/-/lodash-4.17.7.tgz", "integrity": "sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA=="}, "node_modules/@types/lodash-es": {"version": "4.17.12", "resolved": "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.12.tgz", "integrity": "sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/qs": {"version": "6.9.17", "resolved": "https://registry.npmmirror.com/@types/qs/-/qs-6.9.17.tgz", "integrity": "sha512-rX4/bPcfmvxHDv0XjfJELTTr+iB+tn032nPILqHm5wbthUUUuVtNGGqzhya9XUxjTP8Fpr0qYgSZZKxGY++svQ==", "dev": true}, "node_modules/@types/web-bluetooth": {"version": "0.0.20", "resolved": "https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.20.tgz", "integrity": "sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow=="}, "node_modules/@vitejs/plugin-vue": {"version": "5.1.3", "resolved": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-5.1.3.tgz", "integrity": "sha512-3xbWsKEKXYlmX82aOHufFQVnkbMC/v8fLpWwh6hWOUrK5fbbtBh9Q/WWse27BFgSy2/e2c0fz5Scgya9h2GLhw==", "dev": true, "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/compiler-core": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.5.3.tgz", "integrity": "sha512-adAfy9boPkP233NTyvLbGEqVuIfK/R0ZsBsIOW4BZNfb4BRpRW41Do1u+ozJpsb+mdoy80O20IzAsHaihRb5qA==", "dependencies": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.3", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.3.tgz", "integrity": "sha512-wnzFArg9zpvk/811CDOZOadJRugf1Bgl/TQ3RfV4nKfSPok4hi0w10ziYUQR6LnnBAUlEXYLUfZ71Oj9ds/+QA==", "dependencies": {"@vue/compiler-core": "3.5.3", "@vue/shared": "3.5.3"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.3.tgz", "integrity": "sha512-P3uATLny2tfyvMB04OQFe7Sczteno7SLFxwrOA/dw01pBWQHB5HL15a8PosoNX2aG/EAMGqnXTu+1LnmzFhpTQ==", "dependencies": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.3", "@vue/compiler-dom": "3.5.3", "@vue/compiler-ssr": "3.5.3", "@vue/shared": "3.5.3", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.44", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.3.tgz", "integrity": "sha512-F/5f+r2WzL/2YAPl7UlKcJWHrvoZN8XwEBLnT7S4BXwncH25iDOabhO2M2DWioyTguJAGavDOawejkFXj8EM1w==", "dependencies": {"@vue/compiler-dom": "3.5.3", "@vue/shared": "3.5.3"}}, "node_modules/@vue/devtools-api": {"version": "6.6.3", "resolved": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.3.tgz", "integrity": "sha512-0MiMsFma/HqA6g3KLKn+AGpL1kgKhFWszC9U29NfpWK5LE7bjeXxySWJrOJ77hBz+TBrBQ7o4QJqbPbqbs8rJw=="}, "node_modules/@vue/reactivity": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.3.tgz", "integrity": "sha512-2w61UnRWTP7+rj1H/j6FH706gRBHdFVpIqEkSDAyIpafBXYH8xt4gttstbbCWdU3OlcSWO8/3mbKl/93/HSMpw==", "dependencies": {"@vue/shared": "3.5.3"}}, "node_modules/@vue/runtime-core": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.5.3.tgz", "integrity": "sha512-5b2AQw5OZlmCzSsSBWYoZOsy75N4UdMWenTfDdI5bAzXnuVR7iR8Q4AOzQm2OGoA41xjk53VQKrqQhOz2ktWaw==", "dependencies": {"@vue/reactivity": "3.5.3", "@vue/shared": "3.5.3"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.5.3.tgz", "integrity": "sha512-wPR1DEGc3XnQ7yHbmkTt3GoY0cEnVGQnARRdAkDzZ8MbUKEs26gogCQo6AOvvgahfjIcnvWJzkZArQ1fmWjcSg==", "dependencies": {"@vue/reactivity": "3.5.3", "@vue/runtime-core": "3.5.3", "@vue/shared": "3.5.3", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.3.tgz", "integrity": "sha512-28volmaZVG2PGO3V3+gBPKoSHvLlE8FGfG/GKXKkjjfxLuj/50B/0OQGakM/g6ehQeqCrZYM4eHC4Ks48eig1Q==", "dependencies": {"@vue/compiler-ssr": "3.5.3", "@vue/shared": "3.5.3"}, "peerDependencies": {"vue": "3.5.3"}}, "node_modules/@vue/shared": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/@vue/shared/-/shared-3.5.3.tgz", "integrity": "sha512-Jp2v8nylKBT+PlOUjun2Wp/f++TfJVFjshLzNtJDdmFJabJa7noGMncqXRM1vXGX+Yo2V7WykQFNxusSim8SCA=="}, "node_modules/@vueuse/core": {"version": "10.11.1", "resolved": "https://registry.npmmirror.com/@vueuse/core/-/core-10.11.1.tgz", "integrity": "sha512-guoy26JQktXPcz+0n3GukWIy/JDNKti9v6VEMu6kV2sYBsWuGiTU8OWdg+ADfUbHg3/3DlqySDe7JmdHrktiww==", "dependencies": {"@types/web-bluetooth": "^0.0.20", "@vueuse/metadata": "10.11.1", "@vueuse/shared": "10.11.1", "vue-demi": ">=0.14.8"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "10.11.1", "resolved": "https://registry.npmmirror.com/@vueuse/metadata/-/metadata-10.11.1.tgz", "integrity": "sha512-IGa5FXd003Ug1qAZmyE8wF3sJ81xGLSqTqtQ6jaVfkeZ4i5kS2mwQF61yhVqojRnenVew5PldLyRgvdl4YYuSw==", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "10.11.1", "resolved": "https://registry.npmmirror.com/@vueuse/shared/-/shared-10.11.1.tgz", "integrity": "sha512-LHpC8711VFZlDaYUXEBbFBCQ7GS3dVU9mjOhhMhXP6txTV4EhYQg/KGnQuvt/sPAtoUKq7VVUnL6mVtFoL42sA==", "dependencies": {"vue-demi": ">=0.14.8"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/ansi-regex": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.1.0.tgz", "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz", "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==", "dev": true}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aos": {"version": "2.3.4", "resolved": "https://registry.npmmirror.com/aos/-/aos-2.3.4.tgz", "integrity": "sha512-zh/ahtR2yME4I51z8IttIt4lC1Nw0ktsFtmeDzID1m9naJnWXhCoARaCgNOGXb5CLy3zm+wqmRAEgMYB5E2HUw==", "dependencies": {"classlist-polyfill": "^1.0.3", "lodash.debounce": "^4.0.6", "lodash.throttle": "^4.0.1"}}, "node_modules/arg": {"version": "5.0.2", "resolved": "https://registry.npmmirror.com/arg/-/arg-5.0.2.tgz", "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==", "dev": true}, "node_modules/async-validator": {"version": "4.2.5", "resolved": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz", "integrity": "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg=="}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/autoprefixer": {"version": "10.4.20", "resolved": "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-10.4.20.tgz", "integrity": "sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"browserslist": "^4.23.3", "caniuse-lite": "^1.0.30001646", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.0.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/axios": {"version": "1.7.7", "resolved": "https://registry.npmmirror.com/axios/-/axios-1.7.7.tgz", "integrity": "sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "dev": true, "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.4", "resolved": "https://registry.npmmirror.com/browserslist/-/browserslist-4.24.4.tgz", "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-builder": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/buffer-builder/-/buffer-builder-0.2.0.tgz", "integrity": "sha512-7VPMEPuYznPSoR21NE1zvd2Xna6c/CloiZCfcMXR1Jny6PjX0N4Nsa38zcBFo/FMK+BlA+FLKbJCQ0i2yxp+Xg==", "dev": true}, "node_modules/call-bind": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.7.tgz", "integrity": "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camelcase-css": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/camelcase-css/-/camelcase-css-2.0.1.tgz", "integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001692", "resolved": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001692.tgz", "integrity": "sha512-A95VKan0kdtrsnMubMKxEKUKImOPSuCpYgxSQBo036P5YYgVIcOYJEgt/txJWqObiRQeISNCfef9nvlQ0vbV7A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "dev": true, "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/classlist-polyfill": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/classlist-polyfill/-/classlist-polyfill-1.2.0.tgz", "integrity": "sha512-GzIjNdcEtH4ieA2S8NmrSxv7DfEV5fmixQeyTmqmRmRJPGpRBaSnA2a0VrCjyT8iW8JjEdMbKzDotAJf+ajgaQ=="}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz", "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==", "dev": true, "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/didyoumean": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/didyoumean/-/didyoumean-1.2.2.tgz", "integrity": "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==", "dev": true}, "node_modules/dlv": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/dlv/-/dlv-1.1.3.tgz", "integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==", "dev": true}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==", "dev": true}, "node_modules/electron-to-chromium": {"version": "1.5.80", "resolved": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.80.tgz", "integrity": "sha512-LTrKpW0AqIuHwmlVNV+cjFYTnXtM9K37OGhpe0ZI10ScPSxqVSryZHIY3WnCS5NSYbBODRTZyhRMS2h5FAEqAw==", "dev": true}, "node_modules/element-plus": {"version": "2.8.2", "resolved": "https://registry.npmmirror.com/element-plus/-/element-plus-2.8.2.tgz", "integrity": "sha512-pqoQlaUmzUFCjjTHyxGO7Cd0CizsQpIaad1ozV9PCaYjh2T4MIMnjfifqiYs2lWoZ/8GVwrRG1WTCfnZEjwfcg==", "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.3", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/element-plus/node_modules/@types/web-bluetooth": {"version": "0.0.16", "resolved": "https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz", "integrity": "sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ=="}, "node_modules/element-plus/node_modules/@vueuse/core": {"version": "9.13.0", "resolved": "https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz", "integrity": "sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/element-plus/node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/element-plus/node_modules/@vueuse/metadata": {"version": "9.13.0", "resolved": "https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz", "integrity": "sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/element-plus/node_modules/@vueuse/shared": {"version": "9.13.0", "resolved": "https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz", "integrity": "sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==", "dependencies": {"vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/element-plus/node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "hasInstallScript": true, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==", "dev": true}, "node_modules/entities": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/es-define-property": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.0.tgz", "integrity": "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==", "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.21.5", "resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.21.5.tgz", "integrity": "sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==", "dev": true, "hasInstallScript": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dev": true, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fastq": {"version": "1.18.0", "resolved": "https://registry.npmmirror.com/fastq/-/fastq-1.18.0.tgz", "integrity": "sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==", "dev": true, "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/font-awesome": {"version": "4.7.0", "resolved": "https://registry.npmmirror.com/font-awesome/-/font-awesome-4.7.0.tgz", "integrity": "sha512-U6kGnykA/6bFmg1M/oT9EkFeIYv7JlX3bozwQJWiiLz6L0w3F5vBVPxHlwyX/vtNq1ckcpRKOB9f2Qal/VtFpg==", "engines": {"node": ">=0.10.3"}}, "node_modules/foreground-child": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/foreground-child/-/foreground-child-3.3.0.tgz", "integrity": "sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==", "dev": true, "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fraction.js": {"version": "4.3.7", "resolved": "https://registry.npmmirror.com/fraction.js/-/fraction.js-4.3.7.tgz", "integrity": "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==", "dev": true, "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz", "integrity": "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/glob": {"version": "10.4.5", "resolved": "https://registry.npmmirror.com/glob/-/glob-10.4.5.tgz", "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==", "dev": true, "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "dev": true, "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/gopd": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz", "integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gsap": {"version": "3.12.5", "resolved": "https://registry.npmmirror.com/gsap/-/gsap-3.12.5.tgz", "integrity": "sha512-srBfnk4n+Oe/ZnMIOXt3gT605BX9x5+rh/prT2F1SsNJsU1XuMiP0E2aptW481OnonOGACZWBqseH5Z7csHxhQ=="}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.3.tgz", "integrity": "sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/immutable": {"version": "4.3.7", "resolved": "https://registry.npmmirror.com/immutable/-/immutable-4.3.7.tgz", "integrity": "sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==", "dev": true}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "dev": true, "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "dev": true, "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true}, "node_modules/jackspeak": {"version": "3.4.3", "resolved": "https://registry.npmmirror.com/jackspeak/-/jackspeak-3.4.3.tgz", "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==", "dev": true, "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jiti": {"version": "1.21.7", "resolved": "https://registry.npmmirror.com/jiti/-/jiti-1.21.7.tgz", "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==", "dev": true, "bin": {"jiti": "bin/jiti.js"}}, "node_modules/js-cookie": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/js-cookie/-/js-cookie-3.0.5.tgz", "integrity": "sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==", "engines": {"node": ">=14"}}, "node_modules/lilconfig": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/lilconfig/-/lilconfig-3.1.3.tgz", "integrity": "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==", "dev": true, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dev": true}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="}, "node_modules/lodash-unified": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz", "integrity": "sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==", "peerDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}}, "node_modules/lodash.debounce": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="}, "node_modules/lodash.throttle": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz", "integrity": "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="}, "node_modules/lru-cache": {"version": "10.4.3", "resolved": "https://registry.npmmirror.com/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "dev": true}, "node_modules/magic-string": {"version": "0.30.11", "resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.11.tgz", "integrity": "sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/memoize-one": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz", "integrity": "sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw=="}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "9.0.5", "resolved": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "dev": true, "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz", "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "dev": true, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mz": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz", "integrity": "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==", "dev": true, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.7", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz", "integrity": "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-wheel-es": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz", "integrity": "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw=="}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/object-hash/-/object-hash-3.0.0.tgz", "integrity": "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.3", "resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.3.tgz", "integrity": "sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==", "dev": true}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "dev": true, "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/pify/-/pify-2.3.0.tgz", "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.6", "resolved": "https://registry.npmmirror.com/pirates/-/pirates-4.0.6.tgz", "integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/postcss": {"version": "8.4.49", "resolved": "https://registry.npmmirror.com/postcss/-/postcss-8.4.49.tgz", "integrity": "sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-import": {"version": "15.1.0", "resolved": "https://registry.npmmirror.com/postcss-import/-/postcss-import-15.1.0.tgz", "integrity": "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==", "dev": true, "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-js": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/postcss-js/-/postcss-js-4.0.1.tgz", "integrity": "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==", "dev": true, "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/postcss-load-config": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/postcss-nested": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/postcss-nested/-/postcss-nested-6.2.0.tgz", "integrity": "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "resolved": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "integrity": "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==", "dev": true, "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/qs": {"version": "6.13.0", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.13.0.tgz", "integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/read-cache": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/read-cache/-/read-cache-1.0.0.tgz", "integrity": "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==", "dev": true, "dependencies": {"pify": "^2.3.0"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "dev": true, "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "dev": true, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/reusify": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true, "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rollup": {"version": "4.21.2", "resolved": "https://registry.npmmirror.com/rollup/-/rollup-4.21.2.tgz", "integrity": "sha512-e3TapAgYf9xjdLvKQCkQTnbTKd4a6jwlpQSJJFokHGaX2IVjoEqkIIhiQfqsi0cdwlOD+tQGuOd5AJkc5RngBw==", "dev": true, "dependencies": {"@types/estree": "1.0.5"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.21.2", "@rollup/rollup-android-arm64": "4.21.2", "@rollup/rollup-darwin-arm64": "4.21.2", "@rollup/rollup-darwin-x64": "4.21.2", "@rollup/rollup-linux-arm-gnueabihf": "4.21.2", "@rollup/rollup-linux-arm-musleabihf": "4.21.2", "@rollup/rollup-linux-arm64-gnu": "4.21.2", "@rollup/rollup-linux-arm64-musl": "4.21.2", "@rollup/rollup-linux-powerpc64le-gnu": "4.21.2", "@rollup/rollup-linux-riscv64-gnu": "4.21.2", "@rollup/rollup-linux-s390x-gnu": "4.21.2", "@rollup/rollup-linux-x64-gnu": "4.21.2", "@rollup/rollup-linux-x64-musl": "4.21.2", "@rollup/rollup-win32-arm64-msvc": "4.21.2", "@rollup/rollup-win32-ia32-msvc": "4.21.2", "@rollup/rollup-win32-x64-msvc": "4.21.2", "fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.1", "resolved": "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz", "integrity": "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==", "dev": true, "dependencies": {"tslib": "^2.1.0"}}, "node_modules/sass-embedded": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded/-/sass-embedded-1.78.0.tgz", "integrity": "sha512-NR2kvhWVFABmBm0AqgFw9OweQycs0Qs+/teJ9Su+BUY7up+f8S5F/Zi+7QtAqJlewsQyUNfzm1vRuM+20lBwRQ==", "dev": true, "dependencies": {"@bufbuild/protobuf": "^1.0.0", "buffer-builder": "^0.2.0", "immutable": "^4.0.0", "rxjs": "^7.4.0", "supports-color": "^8.1.1", "varint": "^6.0.0"}, "bin": {"sass": "dist/bin/sass.js"}, "engines": {"node": ">=16.0.0"}, "optionalDependencies": {"sass-embedded-android-arm": "1.78.0", "sass-embedded-android-arm64": "1.78.0", "sass-embedded-android-ia32": "1.78.0", "sass-embedded-android-riscv64": "1.78.0", "sass-embedded-android-x64": "1.78.0", "sass-embedded-darwin-arm64": "1.78.0", "sass-embedded-darwin-x64": "1.78.0", "sass-embedded-linux-arm": "1.78.0", "sass-embedded-linux-arm64": "1.78.0", "sass-embedded-linux-ia32": "1.78.0", "sass-embedded-linux-musl-arm": "1.78.0", "sass-embedded-linux-musl-arm64": "1.78.0", "sass-embedded-linux-musl-ia32": "1.78.0", "sass-embedded-linux-musl-riscv64": "1.78.0", "sass-embedded-linux-musl-x64": "1.78.0", "sass-embedded-linux-riscv64": "1.78.0", "sass-embedded-linux-x64": "1.78.0", "sass-embedded-win32-arm64": "1.78.0", "sass-embedded-win32-ia32": "1.78.0", "sass-embedded-win32-x64": "1.78.0"}}, "node_modules/sass-embedded-android-arm": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-android-arm/-/sass-embedded-android-arm-1.78.0.tgz", "integrity": "sha512-YM6nrmKsj+ImaSTd96F+jzbWSbhPkRN4kedbLgIJ5FsILNa9NAqhmrCQz9pdcjuAhyfxWImdUACsT23CPGENZQ==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-arm64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-android-arm64/-/sass-embedded-android-arm64-1.78.0.tgz", "integrity": "sha512-2sAr11EgwPudAuyk4Ite+fWGYJspiFSiZDU2D8/vjjI7BaB9FG6ksYqww3svoMMnjPUWBCjKPDELpZTxViLJbw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-ia32": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-android-ia32/-/sass-embedded-android-ia32-1.78.0.tgz", "integrity": "sha512-TyJOo4TgnHpOfC/PfqCBqd+jGRanWoRd4Br/0KAfIvaIFjTGIPdk26vUyDVugV1J8QUEY4INGE8EXAuDeRldUQ==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-riscv64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-android-riscv64/-/sass-embedded-android-riscv64-1.78.0.tgz", "integrity": "sha512-wwajpsVRuhb7ixrkA3Yu60V2LtROYn45PIYeda30/MrMJi9k3xEqHLhodTexFm6wZoKclGSDZ6L9U5q0XyRKiQ==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-x64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-android-x64/-/sass-embedded-android-x64-1.78.0.tgz", "integrity": "sha512-k5l66PO0LgSHMDbDzAQ/vqrXMlJ3r42ZHJA8MJvUbA6sQxTzDS381V7L+EhOATwyI225j2FhEeTHW6rr4WBQzA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-darwin-arm64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-darwin-arm64/-/sass-embedded-darwin-arm64-1.78.0.tgz", "integrity": "sha512-3<PERSON>axceFSR6N+a22hPYYkj1p45eBaWTt/M8MPTbfzU3TGZrU9bmRX7WlUVtXTo1yYI2iMf22nCv0PQ5ExFF3FMQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-darwin-x64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-darwin-x64/-/sass-embedded-darwin-x64-1.78.0.tgz", "integrity": "sha512-UMTijqE3fJ8vEaaD7GPG7G3GsHuPKOdpS8vuA2v2uwO3BPFp/rEKah66atvGqvGO+0JYApkSv0YTnnexSrkHIQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-arm": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-arm/-/sass-embedded-linux-arm-1.78.0.tgz", "integrity": "sha512-JafT+Co0RK8oO3g9TfVRuG7tkYeh35yDGTgqCFxLrktnkiw5pmIagCfpjxk5GBcSfJMOzhCgclTCDJWAuHGuMQ==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-arm64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-arm64/-/sass-embedded-linux-arm64-1.78.0.tgz", "integrity": "sha512-juMIMpp3DIAiQ842y+boqh0u2SjN4m3mDKrDfMuBznj8DSQoy9J/3e4hLh3g+p0/j83WuROu5nNoYxm2Xz8rww==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-ia32": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-ia32/-/sass-embedded-linux-ia32-1.78.0.tgz", "integrity": "sha512-Gy8GW5g6WX9t8CT2Dto5AL6ikB+pG7aAXWXvfu3RFHktixSwSbyy6CeGqSk1t0xyJCFkQQA/V8HU9bNdeHiBxg==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-arm": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-musl-arm/-/sass-embedded-linux-musl-arm-1.78.0.tgz", "integrity": "sha512-DUVXtcsfsiOJ2Zwp4Y3T6KZWX8h0gWpzmFUrx+gSIbg67vV8Ww2DWMjWRwqLe7HOLTYBegMBYpMgMgZiPtXhIA==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-arm64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-musl-arm64/-/sass-embedded-linux-musl-arm64-1.78.0.tgz", "integrity": "sha512-Lu/TlRHbe9aJY7B7PwWCJz7pTT5Rc50VkApWEmPiU/nu0mGbSpg0Xwar6pNeG8+98ubgKKdRb01N3bvclf5a4A==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-ia32": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-musl-ia32/-/sass-embedded-linux-musl-ia32-1.78.0.tgz", "integrity": "sha512-1E5ywUnq6MRPAecr2r/vDOBr93wXyculEmfyF5JRG8mUufMaxGIhfx64OQE6Drjs+EDURcYZ+Qcg6/ubJWqhcw==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-riscv64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-musl-riscv64/-/sass-embedded-linux-musl-riscv64-1.78.0.tgz", "integrity": "sha512-YvQEvX7ctn5BwC79+HBagDYIciEkwcl2NLgoydmEsBO/0+ncMKSGnjsn/iRzErbq1KJNyjGEni8eSHlrtQI1vQ==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-x64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-musl-x64/-/sass-embedded-linux-musl-x64-1.78.0.tgz", "integrity": "sha512-azdUcZZvZmtUBslIKr2/l4aQrTX7BvO96TD0GLdWz9vuXZrokYm09AJZEnb5j6Pk5I4Xr0yM6BG1Vgcbzqi5Zg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-riscv64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-riscv64/-/sass-embedded-linux-riscv64-1.78.0.tgz", "integrity": "sha512-g8M6vqHMjZUoH9C1WJsgwu+qmwdJAAMDaJTM1emeAScUZMTaQGzm+Q6C5oSGnAGR3XLT/drgbHhbmruXDgkdeQ==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-x64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-linux-x64/-/sass-embedded-linux-x64-1.78.0.tgz", "integrity": "sha512-m997ThzpMwql4u6LzZCoHPIQkgK6bbLPLc7ydemo2Wusqzh6j8XAGxVT5oANp6s2Dmj+yh49pKDozal+tzEX9w==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-arm64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-win32-arm64/-/sass-embedded-win32-arm64-1.78.0.tgz", "integrity": "sha512-qTLIIC5URYRmeuYYllfoL0K1cHSUd+f3sFHAA6fjtdgf288usd6ToCbWpuFb0BtVceEfGQX8lEp+teOG7n7Quw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-ia32": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-win32-ia32/-/sass-embedded-win32-ia32-1.78.0.tgz", "integrity": "sha512-BrOWh18T6Y9xgCokGXElEnd8j03fO4W83bwJ9wHRRkrQWaeHtHs3XWW0fX1j2brngWUTjU+jcYUijWF1Z60krw==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-x64": {"version": "1.78.0", "resolved": "https://registry.npmmirror.com/sass-embedded-win32-x64/-/sass-embedded-win32-x64-1.78.0.tgz", "integrity": "sha512-C14iFDJd7oGhmQehRiEL7GtzMmLwubcDqsBarQ+u9LbHoDlUQfIPd7y8mVtNgtxJCdrAO/jc5qR4C+85yE3xPQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.6.tgz", "integrity": "sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "dev": true, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "engines": {"node": ">=0.10.0"}}, "node_modules/string-width": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==", "dev": true, "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==", "dev": true, "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/sucrase": {"version": "3.35.0", "resolved": "https://registry.npmmirror.com/sucrase/-/sucrase-3.35.0.tgz", "integrity": "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/supports-color": {"version": "8.1.1", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tailwindcss": {"version": "3.4.17", "resolved": "https://registry.npmmirror.com/tailwindcss/-/tailwindcss-3.4.17.tgz", "integrity": "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==", "dev": true, "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.6.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.2", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "micromatch": "^4.0.8", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "resolve": "^1.22.8", "sucrase": "^3.35.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/thenify": {"version": "3.3.1", "resolved": "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz", "integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==", "dev": true, "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz", "integrity": "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==", "dev": true, "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/ts-interface-checker": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "integrity": "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==", "dev": true}, "node_modules/tslib": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "dev": true}, "node_modules/tsparticles": {"version": "3.7.1", "resolved": "https://registry.npmmirror.com/tsparticles/-/tsparticles-3.7.1.tgz", "integrity": "sha512-NNkOYIo01eHpDuaJxDCGgcLEMZKEJTCN/XPVCLg7VxgEWN19rjXpDnDguISxadS8GSFPws7hpGgbeDDAm3MX+Q==", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "dependencies": {"@tsparticles/engine": "3.7.1", "@tsparticles/interaction-external-trail": "3.7.1", "@tsparticles/plugin-absorbers": "3.7.1", "@tsparticles/plugin-emitters": "3.7.1", "@tsparticles/plugin-emitters-shape-circle": "3.7.1", "@tsparticles/plugin-emitters-shape-square": "3.7.1", "@tsparticles/shape-text": "3.7.1", "@tsparticles/slim": "3.7.1", "@tsparticles/updater-destroy": "3.7.1", "@tsparticles/updater-roll": "3.7.1", "@tsparticles/updater-tilt": "3.7.1", "@tsparticles/updater-twinkle": "3.7.1", "@tsparticles/updater-wobble": "3.7.1"}}, "node_modules/tsparticles-engine": {"version": "2.12.0", "resolved": "https://registry.npmmirror.com/tsparticles-engine/-/tsparticles-engine-2.12.0.tgz", "integrity": "sha512-ZjDIYex6jBJ4iMc9+z0uPe7SgBnmb6l+EJm83MPIsOny9lPpetMsnw/8YJ3xdxn8hV+S3myTpTN1CkOVmFv0QQ==", "deprecated": "starting from tsparticles v3 the packages are now moved to @tsparticles/package-name instead of tsparticles-package-name", "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "hasInstallScript": true}, "node_modules/update-browserslist-db": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.2.tgz", "integrity": "sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "dev": true}, "node_modules/varint": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/varint/-/varint-6.0.0.tgz", "integrity": "sha512-cXEIW6cfr15lFv563k4GuVuW/fiwjknytD37jIOLSdSWuOI6WnO/oKwmP2FQTU2l01LP8/M5TSAJpzUaGe3uWg==", "dev": true}, "node_modules/vite": {"version": "5.4.3", "resolved": "https://registry.npmmirror.com/vite/-/vite-5.4.3.tgz", "integrity": "sha512-IH+nl64eq9lJjFqU+/yrRnrHPVTlgy42/+IzbOdaFDVlyLgI/wDlf+FCobXLX1cT0X5+7LMyH1mIy2xJdLfo8Q==", "dev": true, "dependencies": {"esbuild": "^0.21.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vue": {"version": "3.5.3", "resolved": "https://registry.npmmirror.com/vue/-/vue-3.5.3.tgz", "integrity": "sha512-xvRbd0HpuLovYbOHXRHlSBsSvmUJbo0pzbkKTApWnQGf3/cu5Z39mQeA5cZdLRVIoNf3zI6MSoOgHUT5i2jO+Q==", "dependencies": {"@vue/compiler-dom": "3.5.3", "@vue/compiler-sfc": "3.5.3", "@vue/runtime-dom": "3.5.3", "@vue/server-renderer": "3.5.3", "@vue/shared": "3.5.3"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-router": {"version": "4.4.3", "resolved": "https://registry.npmmirror.com/vue-router/-/vue-router-4.4.3.tgz", "integrity": "sha512-sv6wmNKx2j3aqJQDMxLFzs/u/mjA9Z5LCgy6BE0f7yFWMjrPLnS/sPNn8ARY/FXw6byV18EFutn5lTO6+UsV5A==", "dependencies": {"@vue/devtools-api": "^6.6.3"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vuex": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/vuex/-/vuex-4.1.0.tgz", "integrity": "sha512-hmV6UerDrPcgbSy9ORAtNXDr9M4wlNP4pEFKye4ujJF8oqgFFuxDCdOLS3eNoRTtq5O3hoBDh9Doj1bQMYHRbQ==", "dependencies": {"@vue/devtools-api": "^6.0.0-beta.11"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==", "dev": true, "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/yaml": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/yaml/-/yaml-2.7.0.tgz", "integrity": "sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==", "dev": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}}}