<template>
  <el-header v-if="showHeaderAndSidebar && !loading" class="header">
    <div class="header-content">
      <router-link to="/" class="logo">Fuzzing Platform</router-link>
      <el-tooltip content="登出" placement="bottom">
        <el-button type="text" @click="logout" class="logout-btn">
          <el-icon><SwitchButton /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </el-header>
</template>

<script>
import { SwitchButton } from '@element-plus/icons-vue'

export default {
  components: {
    SwitchButton,
  },
  props: {
    showHeaderAndSidebar: Boolean,
    loading: Boolean,
  },
  methods: {
    logout() {
      this.$emit('logout');
    },
  },
};
</script>

<style scoped>
.header {
  background: #ffffff;
  padding: 0;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #E4E7ED;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  /* max-width: 1200px; */
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.logo {
  /* color: #4ED9F5; */
  font-family: 'melete';
  font-size: 24px;
  font-weight: bold;
  text-decoration: none;
  /* font-family:Verdana, Geneva, Tahoma, sans-serif; */
  margin-right: auto;
  background: linear-gradient(90deg, #4ED9F5, #3EAEC2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  
}

.logout-btn {
  color: #606266;
  font-size: 20px;
  transition: color 0.3s ease, transform 0.3s ease;
  margin-right: 10px;
}

.logout-btn:hover {
  color: #4ED9F5;
  transform: scale(1.1);
}
</style>
