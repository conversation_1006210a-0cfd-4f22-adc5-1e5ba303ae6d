<template>
  <el-aside
    v-if="showHeaderAndSidebar && !loading"
    :width="isCollapsed ? '64px' : '250px'"
    class="sidebar"
  >
    <el-menu
      :default-active="$route.path"
      router
      :collapse="isCollapsed"
      class="el-menu-vertical"
      text-color="#a0b7d6"
      active-text-color="#409EFF"
    >
      <!-- <el-menu-item index="/dashboard">
        <el-icon><DataAnalysis /></el-icon>
        <span>仪表盘</span>
      </el-menu-item> -->
      <el-menu-item index="/upload">
        <el-icon><Upload /></el-icon>
        <span>数据管理</span>
      </el-menu-item>
      <el-menu-item index="/rules">
        <el-icon><Document /></el-icon>
        <span>规则信息</span>
      </el-menu-item>
      <el-menu-item index="/test-cases">
        <el-icon><Monitor /></el-icon>
        <span>测试用例</span>
      </el-menu-item>
      <el-menu-item index="/vulnerability">
        <el-icon><Warning /></el-icon> <!-- Use 'Warning' as icon -->
        <span>漏洞检测</span>
      </el-menu-item>
      <el-menu-item v-if="isAdmin" index="/admin">
        <el-icon><Setting /></el-icon>
        <span>管理员</span>
      </el-menu-item>
    </el-menu>
    <el-button class="toggle-collapse-btn" @click="toggleCollapse" type="text">
      <el-icon>
        <Fold v-if="!isCollapsed" />
        <Expand v-if="isCollapsed" />
      </el-icon>
    </el-button>
  </el-aside>
</template>

<script>
import { ElIcon, ElMenu, ElMenuItem } from 'element-plus';
import {
  DataAnalysis,
  Monitor,
  Setting,
  Upload,
  Fold,
  Expand,
  Document,
  Warning,  // Use 'Warning' instead of 'Bug'
} from '@element-plus/icons-vue';

export default {
  components: {
    ElMenu,
    ElMenuItem,
    ElIcon,
    DataAnalysis,
    Upload,
    Monitor,
    Setting,
    Fold,
    Expand,
    Document,
    Warning,  // Adjusted the imported icons
  },
  props: {
    showHeaderAndSidebar: Boolean,
    loading: Boolean,
    isCollapsed: Boolean,
    isAdmin: Boolean,
  },
  methods: {
    toggleCollapse() {
      this.$emit('toggle-collapse');
    },
  },
};
</script>

<style scoped>
.sidebar {
  padding-top: 20px;
  position: relative;
  transition: width 0.3s;
  height: 93.5vh;
  background: #ffffff;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-item {
  font-size: 16px;
  margin-bottom: 10px;
  margin-left: 15px;
  height: 50px;
  width: 230px;
  color: #606266;
  transition: all 0.3s ease;
}

.el-menu-item:hover {
  color: #4ED9F5;
  background-color: #EDFBFE;
  border-radius: 8px;
}

.el-menu-item.is-active {
  background: linear-gradient(90deg, #4ED9F5, #ffffff);
  color: #ffffff;
  -webkit-text-fill-color: #ffffff;
  border-radius: 24px;
  border: none;
  position: relative;
  overflow: visible;
  box-shadow: 0 2px 8px rgba(78, 217, 245, 0.3);
}

.el-menu-item i {
  color: #606266;
  transition: color 0.3s ease;
}

.el-menu-item:hover i {
  color: #4ED9F5;
}

.el-menu-item.is-active i {
  color: #ffffff;
}

.toggle-collapse-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: #606266;
  background: transparent;
  border: none;
  border-radius: 50%;
  font-size: 30px;
  transition: color 0.3s ease, transform 0.3s ease;
}

.toggle-collapse-btn:hover {
  color: #4ED9F5;
  transform: translateX(-50%) scale(1.1);
}
</style>
