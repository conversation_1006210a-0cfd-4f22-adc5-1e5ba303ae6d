<template>
  <el-form :model="fuzzingForm" label-width="150px">
    <el-card class="create-case-container" shadow="always">
      <el-form-item label="规则及数据配置" class="form-item">
        <el-select
          v-model="fuzzingForm.ruleId"
          placeholder="请选择规则"
          filterable
        >
          <el-option
            v-for="rule in rules"
            :key="rule.id"
            :label="rule.ruleName"
            :value="rule.id"
          />
        </el-select>
        <el-select
          v-model="fuzzingForm.originalFileId"
          placeholder="请选择数据"
          filterable
        >
          <el-option
            v-for="file in files"
            :key="file.id"
            :label="file.fileName"
            :value="file.id"
            :loading="loadingFileNames"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="大模型" class="form-item">
        <el-radio-group v-model="fuzzingForm.model">
          <el-radio-button label="<PERSON>i"></el-radio-button>
          <el-radio-button label="文心一言"></el-radio-button>
          <el-radio-button label="通义千问"></el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述" class="form-item">
        <el-input
          type="textarea"
          v-model="fuzzingForm.description"
          placeholder="请输入描述"
          rows="3"
          autosize
        ></el-input>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" :disabled="isGenerating" @click="startTest"
          >创建用例</el-button
        >
        <el-button type="primary" @click="clearOptions">清空选项</el-button>
      </el-form-item>
    </el-card>

    <el-progress
      :percentage="progress"
      :stroke-width="5"
      :show-text="false"
      class="custom-progress"
    />
    <el-table
      ref="taskTable"
      :data="tasks"
      :row-class-name="tableRowClassName"
      style="margin-top: 20px"
      height="350"
      stripe
      border
    >
      <el-table-column prop="id" label="用例ID" width="100" sortable />
      <el-table-column prop="fileName" label="用例名称" width="240" />
      <el-table-column prop="description" label="描述" width="280" />
      <el-table-column label="链接" prop="filePath" width="720">
        <template #default="{ row }">
          <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{
            row.filePath
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            size="small"
            type="danger"
            @click="confirmDeleteCase(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import axios from "axios";

export default {
  setup() {
    const fuzzingForm = ref({
      ruleId: null, // 存储选择的规则ID
      originalFileId: null, // 存储选择的数据文件ID
      model: "Kimi",
      description: "",
    });
    const tasks = ref([]);
    const progress = ref(0);
    const loadingFileNames = ref(false);
    const files = ref([]);
    const rules = ref([]);
    const isGenerating = ref(false); // 控制创建用例按钮状态

    // 获取数据文件列表
    const fetchFileNames = async () => {
      loadingFileNames.value = true;
      try {
        const response = await axios.get("/original/list");
        if (response.data.code === 1) {
          files.value = response.data.data.items;
        } else {
          ElMessage.error("加载文件名失败");
        }
      } catch (error) {
        ElMessage.error("请求失败");
      } finally {
        loadingFileNames.value = false;
      }
    };

    // 获取规则列表
    const fetchRuleNames = async () => {
      try {
        const response = await axios.get("/rule/list");
        if (response.data.code === 1) {
          rules.value = response.data.data.items;
        } else {
          ElMessage.error("加载规则失败");
        }
      } catch (error) {
        ElMessage.error("请求失败");
      }
    };

    // 创建用例
    const startTest = async () => {
      if (!fuzzingForm.value.ruleId || !fuzzingForm.value.originalFileId) {
        ElMessage.error("请先选择规则和数据");
        return;
      }
      if (!fuzzingForm.value.description) {
        ElMessage.error("请填写描述信息");
        return;
      }

      isGenerating.value = true;
      progress.value = 0;
      ElMessage.success(`测试已开始，模型 ${fuzzingForm.value.model}`);

      // 准备参数并编码
      const params = new URLSearchParams();
      params.append(
        "originalFileId",
        encodeURIComponent(fuzzingForm.value.originalFileId)
      );
      params.append("ruleId", encodeURIComponent(fuzzingForm.value.ruleId));
      params.append(
        "description",
        encodeURIComponent(fuzzingForm.value.description)
      );

      try {
        // 发送POST请求
        const response = await axios.post(
          "/generate/generateTestFile",
          params,
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        if (response.data.code === 1 && response.data.data.length > 0) {
          // 模拟进度条
          const interval = setInterval(() => {
            if (progress.value >= 100) {
              clearInterval(interval);
              progress.value = 100;
              // 添加新任务到表格，解码描述信息
              response.data.data.forEach((testCase) => {
                tasks.value.push({
                  id: testCase.id,
                  fileName: testCase.fileName,
                  description: decodeURIComponent(testCase.description),
                  filePath: testCase.filePath,
                });
              });
              ElMessage.success("测试完成");
              isGenerating.value = false;
            } else {
              progress.value += 10; // 固定速度，每次增加10%
            }
          }, 500); // 每500毫秒增加一次
        } else {
          ElMessage.error("生成测试用例失败");
          isGenerating.value = false;
        }
      } catch (error) {
        ElMessage.error("请求失败");
        isGenerating.value = false;
      }
    };

    // 清空选项
    const clearOptions = () => {
      fuzzingForm.value.ruleId = null;
      fuzzingForm.value.originalFileId = null;
      fuzzingForm.value.model = "Kimi";
      fuzzingForm.value.testCaseCount = 1;
      fuzzingForm.value.description = "";
      ElMessage.info("选项已清空");
    };

    // 删除用例确认
    const confirmDeleteCase = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确认删除 ${row.fileName} 吗？`,
          "删除确认",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );
        await deleteCase(row);
      } catch {
      }
    };

    const deleteCase = async (row) => {
      await axios.delete(`/testcase/${row.id}`);
      tasks.value = tasks.value.filter((task) => task.id !== row.id);
      ElMessage.success("删除成功");
      fetchData();
    };

    // 表格行样式（可选）
    const tableRowClassName = (row, index) => {
      return "";
    };

    onMounted(() => {
      fetchFileNames();
      fetchRuleNames();
    });

    return {
      fuzzingForm,
      tasks,
      progress,
      loadingFileNames,
      files,
      rules,
      startTest,
      clearOptions,
      confirmDeleteCase,
      tableRowClassName,
      isGenerating,
    };
  },
};
</script>

<style scoped>
.form-item {
  margin-bottom: 15px;
}
.create-case-container {
  margin-bottom: 10px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
}
.el-select {
  width: 45%;
  margin-right: 10px;
}
.form-item-button {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}
.custom-progress {
  margin-top: 20px;
}

/* 添加文本区域样式 */
:deep(.el-textarea__inner) {
  background-color:rgb(255, 255, 255);
  color: #3a3a3a !important;
  border: 1px solid #3a3a3a;
}

:deep(.el-textarea__inner::placeholder) {
  color: #999;
}

:deep(.el-textarea__inner:focus) {
  color:#3a3a3a !important;
}

:deep(.el-form-item__label) {
  color: #fff;
}
</style>
