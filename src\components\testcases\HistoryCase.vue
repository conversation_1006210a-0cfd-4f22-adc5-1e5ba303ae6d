<template>
  <div>
    <div class="data-table">
      <div class="buttons">
        <el-button type="primary" @click="triggerUpload">上传用例</el-button>
        <el-button
          type="danger"
          @click="confirmBatchDelete"
          :disabled="!selectedRows.length"
          >批量删除</el-button
        >
        <input
          ref="fileInput"
          type="file"
          @change="uploadData"
          style="display: none"
        />
      </div>

      <el-table
        :data="paginatedData"
        stripe
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          prop="id"
          label="用例ID"
          width="100"
          sortable
        ></el-table-column>
        <el-table-column label="文件名" width="150" prop="fileName">
          <template #default="scope">
            <div
              v-if="editingId !== scope.row.id"
              @click="startEditing(scope.row)"
            >
              {{ scope.row.fileName }}
            </div>
            <el-input
              v-else
              v-model="editingName"
              @blur="finishEditing(scope.row)"
              placeholder="编辑文件名"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          width="200"
        ></el-table-column>
        <el-table-column label="链接" prop="filePath" width="900">
          <template #default="{ row }">
            <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{
              row.filePath
            }}</a>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              size="small"
              type="danger"
              @click="confirmDeleteCase(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style="margin-top: 20px; text-align: center"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <el-dialog
      title="上传用例"
      v-model="uploadDialogVisible"
      width="30%"
      @close="resetUploadDialog"
    >
      <el-form :model="uploadForm" label-width="80px">
        <el-form-item label="文件">
          <el-upload
            ref="fileUploader"
            :before-upload="validateFile"
            :on-change="handleFileChange"
            :file-list="fileList"
            :limit="1"
            accept=".xml"
            action=""
            :auto-upload="false"
          >
            <el-button> 选择文件 </el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            v-model="uploadForm.description"
            placeholder="请输入描述（可选）"
            rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFile">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import axios from "axios";

const loading = ref(false);
const selectedRows = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const editingId = ref(null);
const editingName = ref("");
const uploadDialogVisible = ref(false);
const uploadForm = ref({
  file: null,
  description: "",
});
const fileList = ref([]);
const loadingFileNames = ref(false);
const caseData = ref([]);
// const tasks = ref([]); // 未在此组件使用，可移除

const tableData = ref([]);
const paginatedData = computed(() => tableData.value);
const total = ref(0);

const fetchData = async () => {
  loading.value = true;
  try {
    const response = await axios.get("/testcase/list", {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      },
    });
    // caseData.value = response.data.data.items || [];
    // tableData.value = caseData.value;
    const result = response.data.data;
    // 对每个测试用例的描述进行解码
    const decodedItems = (result.items || []).map(item => ({
      ...item,
      description: decodeDescription(item.description)
    }));
    caseData.value = decodedItems;
    tableData.value = decodedItems;
    total.value = result.total;
    
  } catch (error) {
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};
const decodeDescription = (description) => {
  try {
    return decodeURIComponent(description);
  } catch (e) {
    // 如果描述信息不是编码格式，直接返回原始值
    return description;
  }
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchData();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  fetchData();
};

const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};

const startEditing = (row) => {
  editingId.value = row.id;
  editingName.value = row.fileName;
};

const finishEditing = async (row) => {
  const { id, fileName: originalFileName } = row;
  const newFileName = editingName.value.trim();
  if (newFileName === originalFileName) {
    editingId.value = null;
    return;
  }
  const isDuplicate = caseData.value.some(
    (data) => data.fileName === newFileName && data.id !== id
  );
  if (isDuplicate) {
    ElMessage.error("文件名已存在，请选择不同的名称");
    editingName.value = originalFileName;
    editingId.value = null;
    return;
  }
  try {
    const payload = { id, fileName: newFileName };
    const response = await axios.put("/testcase", payload);
    if (response.data.msg === "success") {
      ElMessage.success("文件名修改成功");
      row.fileName = newFileName;
    } else {
      ElMessage.error("文件名修改失败，请稍后重试");
    }
  } catch (error) {
    ElMessage.error("文件名修改失败");
  } finally {
    editingId.value = null;
  }
};

const confirmDeleteCase = async (row) => {
  try {
    await ElMessageBox.confirm(`确认删除 ${row.fileName} 吗？`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteCase(row);
  } catch {
    ElMessage.info("取消删除");
  }
};

const deleteCase = async (row) => {
  await axios.delete(`/testcase/${row.id}`);
  caseData.value = caseData.value.filter((item) => item.id !== row.id);
  ElMessage.success("删除成功");
  fetchData();
};

const confirmBatchDelete = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning("请选择要删除的用例");
    return;
  }
  try {
    await ElMessageBox.confirm(
      "此操作将永久删除选中的用例，是否继续？",
      "警告",
      { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
    );
    await batchDelete();
  } catch {
    ElMessage.info("取消删除");
  }
};

const batchDelete = async () => {
  const ids = selectedRows.value.map((row) => row.id).join(",");
  try {
    await axios.delete(`/testcase/${ids}`);
    caseData.value = caseData.value.filter(
      (row) => !selectedRows.value.includes(row)
    );
    ElMessage.success("批量删除成功");
    fetchData();
  } catch {
    ElMessage.error("批量删除失败");
  }
};

const triggerUpload = () => {
  uploadDialogVisible.value = true;
};

const validateFile = (file) => {
  const isXml = file.type === "text/xml";
  if (!isXml) {
    ElMessage.error("只能上传 XML 文件！");
  }
  return isXml;
};

const handleFileChange = (file) => {
  uploadForm.value.file = file.raw;
};

const submitFile = async () => {
  const file = uploadForm.value.file;
  if (!file) {
    ElMessage.error("请先选择文件");
    return;
  }
  const fileName = file.name;
  const description = uploadForm.value.description;
  const formData = new FormData();
  formData.append("file", file);
  try {
    const uploadResponse = await axios.post("/upload", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    if (uploadResponse.data.msg === "success") {
      ElMessage.success("文件已上传到云服务器");
      const filePath = uploadResponse.data.data;
      const saveRequestData = { fileName, filePath, description };
      const saveResponse = await axios.post("/testcase", saveRequestData);
      if (saveResponse.data.msg === "success") {
        ElMessage.success("文件信息已存储到数据库");
        uploadDialogVisible.value = false;
        uploadForm.value.file = null;
        uploadForm.value.description = "";
        fileList.value = [];
        fetchData();
      } else {
        ElMessage.error("文件信息存储失败");
      }
    } else {
      ElMessage.error("文件上传失败");
    }
  } catch (error) {
    ElMessage.error("文件上传过程中发生错误");
  }
};

const resetUploadDialog = () => {
  uploadForm.value.file = null;
  uploadForm.value.description = "";
  fileList.value = [];
};

// 暴露 fetchData 方法给父组件调用
defineExpose({ fetchData });

</script>

<style scoped>
.buttons {
  margin-bottom: 20px;
  text-align: right;
}
.data-table {
  margin-top: 20px;
}
</style>
