<template>
  <div class="capture-config">
    <el-card class="capture-config-form">
      <el-form>
        <el-form-item label="Can接口">
          <el-autocomplete
            v-model="api"
            :fetch-suggestions="querySuggestions"
            placeholder="选择或输入 can 接口"
            @select="handleSelect"
          />
        </el-form-item>

        <!-- 抓包时长（毫秒） -->
        <el-form-item label="抓包时长(ms)">
          <el-input-number
            v-model="captureDuration"
            :min="1000"
            :step="1000"
            placeholder="设置抓包时长"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="startCapture" :disabled="isCapturing">开始抓包</el-button>
          <el-button @click="stopCapture" :disabled="!isCapturing">停止抓包</el-button>
          <!-- 环形进度条 -->
          <el-progress
            type="circle"
            :percentage="captureProgress"
            v-if="isCapturing"
            :format="formatProgress"
            width="25"
            stroke-width="3"
            style="margin-left: 15px"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格：抓包起止时间、抓包时长、Can接口、文件名、链接、操作 -->
    <el-table
      :data="capturedData"
      class="capture-table"
      stripe
      border
      height="400"
    >
      <el-table-column prop="id" label="ID" width="80" sortable />
      <el-table-column prop="startTime" label="抓包起止时间" width="190">
        <template #default="scope">
          <div>{{ scope.row.startTime }} - {{ scope.row.endTime }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="抓包时长(ms)" width="120" />
      <el-table-column prop="api" label="Can接口" width="100" />
      <el-table-column prop="filename" label="文件名" width="200" />
      <el-table-column label="链接">
        <template #default="scope">
          <a :href="scope.row.link" target="_blank">{{ scope.row.link }}</a>
        </template>
      </el-table-column>
      <el-table-column label="操作"  width="80">
        <template #default="scope">
          <!-- <el-button size="small" @click="downloadCapture(scope.row)">下载</el-button> -->
          <el-button size="small" type="danger" @click="deleteCapture(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';

export default {
  setup() {
    const api = ref('');
    const apiOptions = ref([
      { value: 'vcan0', label: 'vcan0' },
      // 其他接口选项
    ]);
    const captureDuration = ref(5000); // 默认5秒，单位毫秒
    const capturedData = ref([]);
    const isCapturing = ref(false);
    const captureProgress = ref(0);
    let captureInterval = null;
    let captureTimeout = null;
    let captureStartTime = null;

    const startCapture = () => {
      if (!api.value) {
        ElMessage.error('请填写 Can 接口');
        return;
      }
      if (captureDuration.value < 1000) {
        ElMessage.error('抓包时长最小为1000毫秒');
        return;
      }

      // 初始化抓包参数和进度
      captureStartTime = new Date();
      isCapturing.value = true;
      captureProgress.value = 0;

      // 开始更新进度条
      captureInterval = setInterval(updateProgress, 100);

      // 发送POST请求，立即发起，不阻塞后续操作
      const axiosPromise = axios({
        method: 'POST',
        url: `/capture?canInterfaceName=${encodeURIComponent(api.value)}&time=${encodeURIComponent(captureDuration.value)}`,
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          canInterfaceName: api.value,
          time: captureDuration.value.toString(),
        },
      }).catch(error => {
        ElMessage.error(`抓包请求错误: ${error.message}`);
        // 若请求出错，可在此处理错误逻辑
      });

      // 设置定时器，在抓包时长结束时停止进度并处理结果
      captureTimeout = setTimeout(async () => {

        let response;
        try {
          // 等待POST请求完成，并获取返回的数据
          response = await axiosPromise;
        } catch (error) {
          ElMessage.error(`请求处理失败: ${error.message}`);
          return;
        }

        const endTime = new Date();

        // 根据接口返回的数据更新表格
        if (response && response.data && response.data.code === 1) {
          const responseData = response.data.data;
          console.log(responseData);
          capturedData.value.push({
            id: responseData.id,
            startTime: captureStartTime.toLocaleTimeString(),
            endTime: endTime.toLocaleTimeString(),
            duration: captureDuration.value,
            api: api.value,
            filename: responseData.fileName,
            link: responseData.filePath,
          });
          ElMessage.success('抓包完成');
          clearInterval(captureInterval);
          captureProgress.value = 100;
          isCapturing.value = false;
        } else {
          ElMessage.error('接口返回错误或数据格式异常');
        }
      }, captureDuration.value);
    };

    const stopCapture = () => {
      // 停止当前抓包操作和进度显示
      if (captureTimeout) clearTimeout(captureTimeout);
      if (captureInterval) clearInterval(captureInterval);
      isCapturing.value = false;
      captureProgress.value = 0;
      ElMessage.info('已停止抓包');
    };

    const updateProgress = () => {
      const elapsed = new Date() - captureStartTime;
      captureProgress.value = Math.min((elapsed / captureDuration.value) * 30, 100);
    };

    const formatProgress = () => {
      return `${Math.round(captureProgress.value)}%`;
    };

    const downloadCapture = (row) => {
      const captureContent = `
<capture>
  <startTime>${row.startTime}</startTime>
  <endTime>${row.endTime}</endTime>
  <duration>${row.duration}</duration>
  <api>${row.api}</api>
</capture>`;
      const blob = new Blob([captureContent], { type: 'application/xml' });
      const url = URL.createObjectURL(blob);
      const linkElement = document.createElement('a');
      linkElement.href = url;
      linkElement.download = `${row.filename || '抓包文件'}`;
      linkElement.click();
      URL.revokeObjectURL(url);
    };

    const deleteCapture = async(row) => {
      try {
        const response = await axios.delete(`/original/${row.id}`);
        if (response.data.code === 1) {
          ElMessage.success('删除成功');
          capturedData.value = capturedData.value.filter(data => data.startTime !== row.startTime);
          fetchData();
        } else {
          ElMessage.error('删除失败，请稍后重试');
        }
      } catch (error) {
        // ElMessage.error('删除失败');
      }
    };

    const handleSelect = (item) => {
      api.value = item.label;
    };

    const querySuggestions = (queryString, callback) => {
      const suggestions = apiOptions.value.filter(option =>
        option.label.toLowerCase().includes(queryString.toLowerCase())
      );
      callback(suggestions);
    };

    return {
      api,
      captureDuration,
      capturedData,
      isCapturing,
      captureProgress,
      startCapture,
      stopCapture,
      updateProgress,
      formatProgress,
      downloadCapture,
      deleteCapture,
      handleSelect,
      querySuggestions
    };
  }
};
</script>

<style scoped>
.capture-config {
  margin-bottom: 20px;
}
.capture-table {
  margin-top: 20px;
}
.capture-config-form {
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 10px;
}
</style>
