<template>
  <div class="data-table">
    <div class="buttons">
      <!-- <el-button type="primary" @click="syncData">同步</el-button> -->
      <el-button type="primary" @click="triggerUpload">上传数据</el-button>
      <el-button type="danger" @click="confirmBatchDelete" :disabled="!selectedRows.length">批量删除</el-button>
      <input ref="fileInput" type="file" @change="uploadData" style="display: none" />
    </div>

    <el-table
      :data="paginatedData"
      style="width: 100%"
      stripe
      border
      v-loading="loading"
      class="data-table"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" sortable />
      <el-table-column label="文件名" width="250" prop="fileName">
        <template #default="scope">
          <div v-if="editingId !== scope.row.id" @click="startEditing(scope.row)">
            {{ scope.row.fileName }}
          </div>
          <el-input
            v-else
            v-model="editingName"
            @blur="finishEditing(scope.row)"
            placeholder="编辑文件名"
          />
        </template>
      </el-table-column>
      <el-table-column label="链接" prop="filePath" width="1000">
        <template #default="{ row }">
          <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{ row.filePath }}</a>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" type="danger" @click="deleteFile(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px; text-align: center;"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    />
  </div>
</template>

<script>
import { ref, computed,defineExpose } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

export default {
  setup() {
    const tableData = ref([]);
    const total = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const loading = ref(false);
    const editingId = ref(null);
    const editingName = ref('');
    const selectedRows = ref([]);
    const fileInput = ref(null);

    const paginatedData = computed(() => tableData.value);

    const fetchData = async () => {
      loading.value = true;
      const params = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      };
      try {
        const response = await axios.get('/original/list', { params });
        tableData.value = response.data.data.items;
        total.value = response.data.data.total;
        ElMessage.success('数据加载成功');
      } catch (error) {
        ElMessage.error('数据加载失败');
      } finally {
        loading.value = false;
      }
    };

    const handlePageSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchData();
    };

    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchData();
    };

    const syncData = () => {
      ElMessage.success('同步成功');
    };

    const triggerUpload = () => {
      if(fileInput.value) {
        fileInput.value.setAttribute('accept', '.xml');
        fileInput.value.click();
      }
    };

    const uploadData = async (event) => {
      const file = event.target.files[0];
      if (!file) {
        ElMessage.warning('请选择要上传的文件');
        return;
      }
      const formData = new FormData();
      formData.append('file', file);

      try {
        const uploadResponse = await axios.post('/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        ElMessage.success('文件上传成功');
        const filePath = uploadResponse.data.data;
        const fileName = file.name;

        const saveResponse = await axios.post('/original', { fileName, filePath });
        if (saveResponse.data.msg === "success") {
          fetchData();
        } else {
          ElMessage.warning('文件上传成功，但文件信息未存储到数据库');
        }
      } catch (error) {
        ElMessage.error(error.response?.data?.message || '文件上传失败');
      }
    };

    const deleteFile = async (row) => {
      try {
        const response = await axios.delete(`/original/${row.id}`);
        if (response.data.code === 1) {
          ElMessage.success('删除成功');
          fetchData();
        } else {
          ElMessage.error('删除失败，请稍后重试');
        }
      } catch (error) {
        ElMessage.error('删除失败');
      }
    };

    const startEditing = (row) => {
      editingId.value = row.id;
      editingName.value = row.fileName;
    };

    const finishEditing = async (row) => {
      const { id, fileName: originalFileName } = row;
      const newFileName = editingName.value.trim();
      if (newFileName === originalFileName) {
        editingId.value = null;
        return;
      }
      const isDuplicate = tableData.value.some(data => data.fileName === newFileName && data.id !== id);
      if (isDuplicate) {
        ElMessage.error('文件名已存在，请选择不同的名称');
        editingName.value = originalFileName;
        editingId.value = null;
        return;
      }
      try {
        const payload = { id, fileName: newFileName };
        const response = await axios.put('/original', payload);
        if (response.data.msg === "success") {
          ElMessage.success('文件名修改成功');
          row.fileName = newFileName;
        } else {
          ElMessage.error('文件名修改失败，请稍后重试');
        }
      } catch (error) {
        ElMessage.error('文件名修改失败');
      } finally {
        editingId.value = null;
      }
    };

    const handleSelectionChange = (rows) => {
      selectedRows.value = rows;
    };

    const confirmBatchDelete = () => {
      ElMessageBox.confirm(
        `此操作将永久删除选中的 ${selectedRows.value.length} 个文件，是否继续？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        batchDelete();
      }).catch(() => {
        ElMessage.info('已取消删除');
      });
    };

    const batchDelete = async () => {
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请选择要删除的文件');
        return;
      }
      const ids = selectedRows.value.map(row => row.id).join(',');
      try {
        const response = await axios.delete(`/original/${ids}`);
        if (response.data.msg === "success") {
          tableData.value = tableData.value.filter(data => !selectedRows.value.some(row => row.id === data.id));
          ElMessage.success('批量删除成功');
        } else {
          ElMessage.error('批量删除失败，请稍后重试');
        }
      } catch (error) {
        ElMessage.error('批量删除失败');
      } finally {
        selectedRows.value = [];
      }
    };

    // 暴露 fetchData 方法给父组件
    defineExpose({
      fetchData,
    });

    return {
      tableData,
      total,
      currentPage,
      pageSize,
      loading,
      editingId,
      editingName,
      selectedRows,
      fileInput,
      paginatedData,
      fetchData,
      handlePageSizeChange,
      handlePageChange,
      syncData,
      triggerUpload,
      uploadData,
      deleteFile,
      startEditing,
      finishEditing,
      handleSelectionChange,
      confirmBatchDelete,
      batchDelete
    };
  },
  mounted() {
    // this.fetchData();
  }
};
</script>

<style scoped>
.buttons {
  margin-bottom: 20px;
  text-align: right;
}
</style>
