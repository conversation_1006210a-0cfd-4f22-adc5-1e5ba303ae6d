import {createApp} from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
// import './assets/styles/element-variables.scss'; // 引入自定义样式
import './styles/element-variables.scss';
import axios from 'axios';
import Cookies from 'js-cookie';
import './index.css'
import 'font-awesome/css/font-awesome.css';
//   引入font awesome图标
/* import the fontawesome core */
import { library } from '@fortawesome/fontawesome-svg-core'
 
/* import font awesome icon component */
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
 
/* import specific icons */
import { faAngleDown,faShieldAlt } from '@fortawesome/free-solid-svg-icons'
library.add(faAngleDown,faShieldAlt)

// 设置全局基础 URL
axios.defaults.baseURL = import.meta.env.VITE_APP_BASE_URL;
// 添加请求拦截器
axios.interceptors.request.use((config) => {
    const token = Cookies.get('access_token'); // 从 Cookies 获取 token
    if (token) {
      config.headers['token'] = `${token}`; // 将 token 添加到请求头
    }
    return config;
  }, (error) => {
    return Promise.reject(error);
  });

const app = createApp(App);
app.component('font-awesome-icon', FontAwesomeIcon)
app.use(router);
app.use(store);
app.use(ElementPlus);
app.mount('#app');
