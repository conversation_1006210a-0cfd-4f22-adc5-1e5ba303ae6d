import {createRouter, createWebHistory} from 'vue-router';
import Home from '../views/Home.vue';
import Login from '../views/Login.vue';
import Dashboard from '../views/Dashboard.vue';
import Admin from '../views/Admin.vue';
import Upload from '../views/Upload.vue';
import FuzzingTest from '../views/FuzzingTest.vue';
import Register from "@/views/Register.vue";
import Rules from "@/views/Rules.vue";
import TestCases from "@/views/TestCases.vue";
import Vulnerability from "@/views/Vulnerability.vue";

const routes = [
    {path: '/', component: Home},
    {path: '/login', component: Login},
    {path: '/register', component: Register},
    {path: '/dashboard', component: Dashboard},
    {path: '/admin', component: Admin},
    {path: '/upload', component: Upload},
    // {path: '/fuzzing-test', component: FuzzingTest},
    {path: '/test-cases', component:TestCases},
    {path: '/rules', component: Rules},
    {path: '/vulnerability', component: Vulnerability},
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

export default router;
