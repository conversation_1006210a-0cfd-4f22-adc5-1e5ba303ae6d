/* 全局样式文件（ src/styles/element-variables.scss） */
:root {
    /* 主色 */
    --el-color-primary: #4ED9F5;
    --el-color-primary-light-3: #7EE3F7;
    --el-color-primary-light-5: #A3ECF9;
    --el-color-primary-light-7: #C8F5FB;
    --el-color-primary-light-8: #DBF8FC;
    --el-color-primary-light-9: #EDFBFE;
    --el-color-primary-dark-2: #3EAEC2;
    
    /* 成功、警告、危险、信息的主题色 */
    --el-color-success: #67C23A;
    --el-color-warning: #E6A23C;
    --el-color-danger: #F56C6C;
    --el-color-info: #909399;

    /* 背景色 */
    --el-bg-color: #ffffff;
    --el-bg-color-overlay: #ffffff;
    --el-bg-color-page: #f5f7fa;
    
    /* 文字颜色 */
    --el-text-color-primary: #303133;
    --el-text-color-regular: #606266;
    --el-text-color-secondary: #909399;
    --el-text-color-placeholder: #C0C4CC;
    
    /* 边框颜色 */
    --el-border-color: #DCDFE6;
    --el-border-color-light: #E4E7ED;
    --el-border-color-lighter: #EBEEF5;
    --el-border-color-extra-light: #F2F6FC;

    /* 表格样式 */
    --el-table-bg-color: #ffffff;
    --el-table-tr-bg-color: #ffffff;
    --el-table-header-bg-color: #f5f7fa;
    --el-table-row-hover-bg-color: #f5f7fa;
    --el-table-border-color: #EBEEF5;
    --el-table-text-color: #606266;
    --el-table-header-text-color: #303133;

    /* 消息提示样式 */
    --el-message-bg-color: #ffffff;
    --el-message-border-color: #DCDFE6;
    --el-message-text-color: #303133;

    /* 自动完成样式 */
    --el-autocomplete-bg-color: #ffffff;
    --el-autocomplete-hover-bg-color: #f5f7fa;
}

/* 全局表格样式 */
.el-table {
  --el-table-bg-color: #ffffff;
  --el-table-tr-bg-color: #ffffff;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f5f7fa;
  --el-table-border-color: #EBEEF5;
  --el-table-text-color: #606266;
  --el-table-header-text-color: #303133;
  
  background-color: #ffffff !important;
  color: #606266 !important;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.el-table th {
  background-color: #f5f7fa !important;
  color: #303133 !important;
  font-weight: 600;
  border-bottom: 1px solid #EBEEF5;
}

.el-table td {
  background-color: #ffffff !important;
  color: #606266 !important;
  border-bottom: 1px solid #EBEEF5;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #f5f7fa !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

.el-table--border th,
.el-table--border td {
  border-right: 1px solid #EBEEF5;
}

.el-table--border::after,
.el-table--border::before {
  background-color: #EBEEF5;
}

/* 文字颜色样式 */
.el-tab-pane {
  color: #606266 !important;
}

.el-tab-pane span {
  color: #606266 !important;
}

/* 标签文字颜色 */
label[for] {
  color: #606266 !important;
}

.el-tab-pane label {
  color: #606266 !important;
}

.el-tab-pane .el-form-item {
  color: #606266 !important;
}

.el-tab-pane .el-form-item__label {
  color: #606266 !important;
}

/* 卡片头部文字颜色 */
.card-header {
  color: #303133 !important;
}

.card-header span {
  color: #303133 !important;
}

/* 时间显示文字颜色 */
.time-display {
  color: #303133 !important;
}

.time-display span {
  color: #303133 !important;
}

.el-pagination {
  color: #303133 !important;
}

.el-pagination .el-pagination__total,
.el-pagination .el-pagination__jump {
  color: #303133 !important;
}

.el-pagination .el-pagination__sizes .el-input .el-input__inner {
  color: #303133 !important;
}

/* 输入框文字颜色 */
.el-input__inner {
  color: #303133 !important;
}

/* 下拉菜单文字颜色 */
.el-select-dropdown__item {
  color: #303133 !important;
  background-color: #ffffff !important;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #ffffff !important;
  color: #4ED9F5 !important;
}

.el-select-dropdown__item.selected {
  background-color: #ffffff !important;
  color: #4ED9F5 !important;
  font-weight: bold;
}

.el-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-select-dropdown__list {
  background-color: #ffffff !important;
}

.el-select-dropdown__wrap {
  background-color: #ffffff !important;
}

/* 下拉箭头样式 */
.el-select-dropdown__arrow {
  background-color: #ffffff !important;
}

.el-select-dropdown__arrow::before {
  background-color: #ffffff !important;
}

.el-select-dropdown__arrow::after {
  background-color: #ffffff !important;
}

/* 对话框文字颜色 */
.el-dialog__title,
.el-dialog__body {
  color: #303133 !important;
}

/* 消息提示样式 */
.el-message {
  background-color: #ffffff !important;
  border-color: #DCDFE6 !important;
  color: #303133 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-message--success {
  background-color: #ffffff !important;
  border-color: #67C23A !important;
}

.el-message--warning {
  background-color: #ffffff !important;
  border-color: #E6A23C !important;
}

.el-message--error {
  background-color: #ffffff !important;
  border-color: #F56C6C !important;
}

.el-message--info {
  background-color: #ffffff !important;
  border-color: #909399 !important;
}

/* 加载中文字颜色 */
.el-loading-text {
  color: #303133 !important;
}

/* Vulnerability.vue 按钮文字颜色 */
.vulnerability-container .el-button {
  color: #ffffff !important;
  transition: all 0.3s ease;
}

.vulnerability-container .el-button--primary {
  color: #ffffff !important;
}

.vulnerability-container .el-button--success {
  color: #ffffff !important;
}

.vulnerability-container .el-button--warning {
  color: #ffffff !important;
}

.vulnerability-container .el-button--danger {
  color: #ffffff !important;
}

.vulnerability-container .el-button--info {
  color: #ffffff !important;
}

/* Vulnerability.vue 按钮悬停效果 */
.vulnerability-container .el-button--default:hover {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #ffffff !important;
}

.vulnerability-container .el-button--default:focus {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #ffffff !important;
}

/* 查看报告按钮样式 */
.view-report-btn {
  transition: all 0.3s ease;
}

.view-report-btn:hover {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #ffffff !important;
}

.view-report-btn:focus {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #ffffff !important;
}

/* 数字输入框样式 */
.el-input-number {
  background-color: #ffffff !important;
}

.el-input-number .el-input__inner {
  background-color: #ffffff !important;
  color: #303133 !important;
}

.el-input-number .el-input-number__decrease,
.el-input-number .el-input-number__increase {
  background-color: #ffffff !important;
  border-color: #DCDFE6 !important;
}

.el-input-number .el-input-number__decrease:hover,
.el-input-number .el-input-number__increase:hover {
  background-color: #f5f7fa !important;
}

.el-input-number .el-input-number__decrease svg,
.el-input-number .el-input-number__increase svg {
  color: #303133 !important;
}

/* 自动完成样式 */
.el-autocomplete {
  background-color: #ffffff !important;
}

.el-autocomplete .el-input__inner {
  background-color: #ffffff !important;
  color: #303133 !important;
}

.el-autocomplete-suggestion {
  background-color: #ffffff !important;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-autocomplete-suggestion__list {
  background-color: #c3c1c1 !important;
}

.el-autocomplete-suggestion__wrap {
  background-color: #ffffff !important;
}

.el-autocomplete-suggestion li {
  color: #303133 !important;
  background-color: #ffffff !important;
}

.el-autocomplete-suggestion li:hover {
  background-color: #ffffff !important;
}

.el-autocomplete-suggestion li.highlighted {
  background-color: #ffffff !important;
  color: #4ED9F5 !important;
}

/* 标签页样式 */
.el-tabs__item {
  color: #606266 !important;
}

.el-tabs__item.is-active {
  color: #4ED9F5 !important;
}

.el-tabs__item:hover {
  color: #4ED9F5 !important;
}

.el-tabs__nav-wrap::after {
  background-color: #DCDFE6 !important;
}

.el-tabs__active-bar {
  background-color: #4ED9F5 !important;
}

/* 按钮悬浮效果 */
.el-button--default:hover {
  background-color: #606266 !important;
  border-color: #606266 !important;
  color: #ffffff !important;
}

.el-button--default:focus {
  background-color: #606266 !important;
  border-color: #606266 !important;
  color: #ffffff !important;
}

/* 按钮默认文字颜色 */
.el-button--default {
  color: #606266 !important;
  border-color: #DCDFE6 !important;
  background-color: #ffffff !important;
}

/* 登录按钮样式 */
.custom-login-button {
  font-size: 18px !important;
  font-weight: 800 !important;
  letter-spacing: 2px !important;
}

  