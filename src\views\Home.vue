<template>
  <div class="min-h-screen bg-gray-900 text-white overflow-x-hidden">
    <!-- Hero Section -->
    <section
      class="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-blue-50 to-white"
    >
      <div class="absolute inset-0 z-0">
        <img
          src="https://ai-public.mastergo.com/ai/img_res/5066ff6207b2fe945fd04e7852b7d616.jpg"
          alt="Background"
          class="w-full h-full object-cover object-center opacity-80"
        />
      </div>
      <!-- 添加渐变过渡层 -->
      <div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#0a1628] via-[#1a2332]/80 to-transparent z-5"></div>

      <div class="relative z-10 text-center px-4">
        <!-- <img src="https://ai-public.mastergo.com/ai/img_res/9db5799e872b53e151928a0dc4c312df.jpg" alt="Logo" class="w-32 h-32 mx-auto mb-6"> -->
        <h1 class="text-7xl font-bold mb-10 text-[#4ED9F5]">
          智能车载系统安全卫士
        </h1>
        <p class="text-2xl mb-8 text-gray-100">大模型赋能，精准检测，为您的车载网络保驾护航</p>
        <!-- 在 template 中 -->
        <el-button
          type="primary"
          class="custom-button text-white font-bold py-4 px-16 transition-all duration-300 text-lg relative overflow-hidden"
          @click="goToLogin"
        >
          立即体验
        </el-button>

        <!-- <button @click="goToLogin" class="bg-[#4ED9F5] text-gray-900 px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#3bc8e4] transition-colors duration-300 !rounded-button whitespace-nowrap get-start-button">立即体验</button> -->
      </div>
      <div
        class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <!-- <i class="fas fa-angle-down text-[#4ED9F5] text-3xl"></i> -->
        <font-awesome-icon
          @click="scrollToCallToAction"
          icon="fa-solid fa-angle-down"
          class="text-[#4ED9F5] text-3xl"
        />
      </div>
    </section>

    <!-- Platform Advantages -->
    <section class="py-20 px-4 dynamic-bg relative overflow-hidden">
      <!-- 动态背景粒子 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="floating-particles"></div>
        <div class="wave-animation"></div>
      </div>

      <div class="relative z-10">
        <h1 class="text-4xl font-bold text-center mb-12 text-[#4ED9F5]">
          平台优势
        </h1>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div
            v-for="(advantage, index) in advantages"
            :key="index"
            class="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-[#4ED9F5]/30 shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-[#4ED9F5]/20 hover:bg-white/20"
          >
            <i :class="advantage.icon + ' text-[#4ED9F5] text-4xl mb-4'"></i>
            <h3 class="text-xl font-semibold mb-2 text-white">{{ advantage.title }}</h3>
            <p class="text-gray-200">{{ advantage.description }}</p>
          </div>
        </div>
      </div>
    </section>
    <!-- Call to Action -->
    <section ref="callToAction" class="py-20 px-4 text-center dynamic-bg-2 relative overflow-hidden">
      <!-- 动态背景效果 -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="geometric-shapes"></div>
        <div class="gradient-orbs"></div>
      </div>

      <div class="relative z-10">
        <h1 class="text-4xl font-bold mb-6 text-[#4ED9F5]">
          开始使用智能车载系统安全卫士
        </h1>
        <p class="text-xl mb-8 text-gray-200">
          立即体验大模型赋能的模糊测试，提升您的车载网络安全
        </p>
        <button
          @click="goToLogin"
          class="bg-[#4ED9F5] text-white px-12 py-4 rounded-full text-xl font-semibold hover:bg-[#3EAEC2] transition-all duration-300 transform hover:scale-105 !rounded-button whitespace-nowrap shadow-lg hover:shadow-xl"
        >
          开始使用
        </button>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-[#0a1628] py-8 px-4 border-t border-[#4ED9F5]/20">
      <div
        class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center"
      >
        <div class="text-gray-400 mb-4 md:mb-0">
          © 2025 智能车载系统卫士. 保留所有权利.
        </div>
        <div class="flex space-x-4">
          <a
            href="#"
            class="text-gray-400 hover:text-[#4ED9F5] transition-colors duration-300"
            >隐私政策</a
          >
          <a
            href="#"
            class="text-gray-400 hover:text-[#4ED9F5] transition-colors duration-300"
            >使用条款</a
          >
          <a
            href="#"
            class="text-gray-400 hover:text-[#4ED9F5] transition-colors duration-300"
            >联系我们</a
          >
        </div>
      </div>
    </footer>

     <!-- 优化的光标跟随效果 -->
    <div ref="cursor" class="cursor-follow"></div>
    <div ref="cursorTrail" class="cursor-trail"></div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import router from "../router";

const advantages = [
  {
    icon: "fas fa-brain",
    title: "智能分析",
    description: "利用大模型技术，智能识别潜在威胁，提高检测准确率",
  },
  {
    icon: "fas fa-shield-alt",
    title: "全面防护",
    description: "覆盖车载网络各个层面，为您的车辆提供全方位安全保障",
  },
  {
    icon: "fas fa-tachometer-alt",
    title: "高效检测",
    description: "快速扫描和分析，及时发现并报告安全隐患",
  },
];

const chartContainer = ref<HTMLElement | null>(null);

const scrollToCallToAction = () => {
  const element = document.querySelector(".py-20");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const goToLogin = () => {
  router.push("/login");
};

const cursor = ref<HTMLElement | null>(null);
const cursorTrail = ref<HTMLElement | null>(null);

onMounted(() => {
  let mouseX = 0;
  let mouseY = 0;
  let cursorX = 0;
  let cursorY = 0;
  let trailX = 0;
  let trailY = 0;

  // 鼠标移动事件
  window.addEventListener('mousemove', (e) => {
    mouseX = e.clientX;
    mouseY = e.clientY;
  });

  // 平滑动画函数
  const animateCursor = () => {
    // 主光标跟随
    cursorX += (mouseX - cursorX) * 0.1;
    cursorY += (mouseY - cursorY) * 0.1;

    // 拖尾光标跟随
    trailX += (mouseX - trailX) * 0.05;
    trailY += (mouseY - trailY) * 0.05;

    if (cursor.value) {
      cursor.value.style.left = `${cursorX}px`;
      cursor.value.style.top = `${cursorY}px`;
    }

    if (cursorTrail.value) {
      cursorTrail.value.style.left = `${trailX}px`;
      cursorTrail.value.style.top = `${trailY}px`;
    }

    requestAnimationFrame(animateCursor);
  };

  animateCursor();

  // 鼠标悬停效果
  const interactiveElements = document.querySelectorAll('button, a, .el-button');
  interactiveElements.forEach(el => {
    el.addEventListener('mouseenter', () => {
      if (cursor.value) cursor.value.classList.add('cursor-hover');
      if (cursorTrail.value) cursorTrail.value.classList.add('cursor-hover');
    });
    el.addEventListener('mouseleave', () => {
      if (cursor.value) cursor.value.classList.remove('cursor-hover');
      if (cursorTrail.value) cursorTrail.value.classList.remove('cursor-hover');
    });
  });
});
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

body {
  font-family: "Roboto", sans-serif;
}

/* 隐藏默认光标 - 仅在桌面端 */
@media (min-width: 768px) {
  * {
    cursor: none !important;
  }
}

/* 优化的光标跟随效果 */
.cursor-follow {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #4ED9F5 0%, #3EAEC2 50%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
  transform: translate(-50%, -50%);
  display: none;
}

@media (min-width: 768px) {
  .cursor-follow {
    display: block;
  }
}

.cursor-trail {
  position: fixed;
  width: 8px;
  height: 8px;
  background: #4ED9F5;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  opacity: 0.6;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px #4ED9F5, 0 0 20px #4ED9F5, 0 0 30px #4ED9F5;
  display: none;
}

@media (min-width: 768px) {
  .cursor-trail {
    display: block;
  }
}

.cursor-follow.cursor-hover {
  transform: translate(-50%, -50%) scale(1.5);
  background: radial-gradient(circle, #4ED9F5 0%, #3EAEC2 30%, transparent 60%);
}

.cursor-trail.cursor-hover {
  transform: translate(-50%, -50%) scale(2);
  opacity: 0.8;
}

/* 动态背景样式 */
.dynamic-bg {
  background: linear-gradient(135deg, #0a1628 0%, #1a2332 25%, #2a3441 50%, #1a2332 75%, #0a1628 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.dynamic-bg-2 {
  background: linear-gradient(45deg, #0a1628 0%, #1e3a5f 25%, #2a4a6b 50%, #1e3a5f 75%, #0a1628 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

/* 动画关键帧 */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes wave {
  0%, 100% { transform: translateX(0px); }
  50% { transform: translateX(20px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

/* 浮动粒子效果 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.floating-particles::before,
.floating-particles::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: #4ED9F5;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 0 10px #4ED9F5;
}

.floating-particles::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.floating-particles::after {
  top: 60%;
  right: 30%;
  animation-delay: 3s;
}

/* 波浪动画 */
.wave-animation {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(0deg, #4ED9F5/10 0%, transparent 100%);
  animation: wave 8s ease-in-out infinite;
}

/* 几何形状动画 */
.geometric-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.geometric-shapes::before,
.geometric-shapes::after {
  content: '';
  position: absolute;
  border: 2px solid #4ED9F5/30;
  animation: rotate 20s linear infinite;
}

.geometric-shapes::before {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  border-radius: 50%;
  animation-delay: 0s;
}

.geometric-shapes::after {
  width: 80px;
  height: 80px;
  bottom: 30%;
  right: 15%;
  transform: rotate(45deg);
  animation-delay: 10s;
}

/* 渐变光球 */
.gradient-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.gradient-orbs::before,
.gradient-orbs::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, #4ED9F5/20 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

.gradient-orbs::before {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 20%;
  animation-delay: 0s;
}

.gradient-orbs::after {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 25%;
  animation-delay: 2s;
}

.swiper-pagination-bullet {
  background-color: #4ED9F5;
}

.swiper-pagination-bullet-active {
  background-color: #3EAEC2;
}

/* 自定义输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"] {
  @apply bg-white border-gray-300 text-gray-700 focus:border-[#4ED9F5] focus:ring-[#4ED9F5];
}

/* 去除number类型输入框的默认箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background: #4ED9F5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3EAEC2;
}

.custom-button {
  position: relative;
  background: #4ED9F5 !important;
  border: 1px solid transparent !important;
  border-radius: 50px !important;
  height: 50px !important;
  color: #ffffff !important;
  font-size: 18px !important;
  overflow: hidden !important;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.custom-button::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 100px;
  background: white;
  bottom: -25px;
  left: 0;
  border: 2px solid white;
  transform: translateX(-50px) rotate(45deg);
  transition: transform 0.5s ease;
}

.custom-button:hover {
  background: transparent !important;
  border-color: #4ED9F5 !important;
  color: #4ED9F5 !important;
}

.custom-button:hover::before {
  transform: translateX(250px) rotate(45deg);
}
</style>

