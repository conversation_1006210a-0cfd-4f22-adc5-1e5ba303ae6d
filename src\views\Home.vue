<template>
  <div class="min-h-screen bg-gray-900 text-white overflow-x-hidden">
    <!-- Hero Section -->
    <section
      class="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-blue-50 to-white"
    >
      <div class="absolute inset-0 z-0">
        <img
          src="https://ai-public.mastergo.com/ai/img_res/5066ff6207b2fe945fd04e7852b7d616.jpg"
          alt="Background"
          class="w-full h-full object-cover object-center opacity-80"
        />
      </div>
      <div class="relative z-10 text-center px-4">
        <!-- <img src="https://ai-public.mastergo.com/ai/img_res/9db5799e872b53e151928a0dc4c312df.jpg" alt="Logo" class="w-32 h-32 mx-auto mb-6"> -->
        <h1 class="text-7xl font-bold mb-10 text-[#4ED9F5]">
          智能车载系统安全卫士
        </h1>
        <p class="text-2xl mb-8 text-gray-100">大模型赋能，精准检测，为您的车载网络保驾护航</p>
        <!-- 在 template 中 -->
        <el-button
          type="primary"
          class="custom-button text-white font-bold py-4 px-16 transition-all duration-300 text-lg relative overflow-hidden"
          @click="goToLogin"
        >
          立即体验
        </el-button>

        <!-- <button @click="goToLogin" class="bg-[#4ED9F5] text-gray-900 px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#3bc8e4] transition-colors duration-300 !rounded-button whitespace-nowrap get-start-button">立即体验</button> -->
      </div>
      <div
        class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <!-- <i class="fas fa-angle-down text-[#4ED9F5] text-3xl"></i> -->
        <font-awesome-icon
          @click="scrollToCallToAction"
          icon="fa-solid fa-angle-down"
          class="text-[#4ED9F5] text-3xl"
        />
      </div>
    </section>

    <!-- Platform Advantages -->
    <section class="py-20 px-4 bg-white">
      <h1 class="text-4xl font-bold text-center mb-12 text-[#4ED9F5]">
        平台优势
      </h1>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div
          v-for="(advantage, index) in advantages"
          :key="index"
          class="bg-white p-6 rounded-lg border border-[#4ED9F5] shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-[#4ED9F5]/20"
        >
          <i :class="advantage.icon + ' text-[#4ED9F5] text-4xl mb-4'"></i>
          <h3 class="text-xl font-semibold mb-2 text-gray-800">{{ advantage.title }}</h3>
          <p class="text-gray-600">{{ advantage.description }}</p>
        </div>
      </div>
    </section>
    <!-- Call to Action -->
    <section ref="callToAction" class="py-20 px-4 text-center bg-gradient-to-b from-white to-blue-50">
      <h1 class="text-4xl font-bold mb-6 text-[#4ED9F5]">
        开始使用智能车载系统安全卫士
      </h1>
      <p class="text-xl mb-8 text-gray-700">
        立即体验大模型赋能的模糊测试，提升您的车载网络安全
      </p>
      <button
        @click="goToLogin"
        class="bg-[#4ED9F5] text-white px-12 py-4 rounded-full text-xl font-semibold hover:bg-[#3EAEC2] transition-all duration-300 transform hover:scale-105 !rounded-button whitespace-nowrap shadow-lg hover:shadow-xl"
      >
        开始使用
      </button>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-8 px-4 border-t border-gray-200">
      <div
        class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center"
      >
        <div class="text-gray-600 mb-4 md:mb-0">
          © 2025 智能车载系统卫士. 保留所有权利.
        </div>
        <div class="flex space-x-4">
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >隐私政策</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >使用条款</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >联系我们</a
          >
        </div>
      </div>
    </footer>

     <!-- 光标跟随效果 -->
    <div ref="cursor" class="hidden md:block fixed w-4 h-4 bg-blue-400 rounded-full pointer-events-none opacity-80 z-50"></div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import router from "../router";

const advantages = [
  {
    icon: "fas fa-brain",
    title: "智能分析",
    description: "利用大模型技术，智能识别潜在威胁，提高检测准确率",
  },
  {
    icon: "fas fa-shield-alt",
    title: "全面防护",
    description: "覆盖车载网络各个层面，为您的车辆提供全方位安全保障",
  },
  {
    icon: "fas fa-tachometer-alt",
    title: "高效检测",
    description: "快速扫描和分析，及时发现并报告安全隐患",
  },
];

const chartContainer = ref<HTMLElement | null>(null);

const scrollToCallToAction = () => {
  const element = document.querySelector(".py-20");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const goToLogin = () => {
  router.push("/login");
};
const cursor = ref<HTMLElement | null>(null);
onMounted(() => {
  window.addEventListener('mousemove', (e) => {
    if (cursor.value) {
      cursor.value.style.left = `${e.clientX}px`;
      cursor.value.style.top = `${e.clientY}px`;
    }
  });
});
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

body {
  font-family: "Roboto", sans-serif;
}

.swiper-pagination-bullet {
  background-color: #4ED9F5;
}

.swiper-pagination-bullet-active {
  background-color: #3EAEC2;
}

/* 自定义输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"] {
  @apply bg-white border-gray-300 text-gray-700 focus:border-[#4ED9F5] focus:ring-[#4ED9F5];
}

/* 去除number类型输入框的默认箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background: #4ED9F5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3EAEC2;
}

.custom-button {
  position: relative;
  background: #4ED9F5 !important;
  border: 1px solid transparent !important;
  border-radius: 50px !important;
  height: 50px !important;
  color: #ffffff !important;
  font-size: 18px !important;
  overflow: hidden !important;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.custom-button::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 100px;
  background: white;
  bottom: -25px;
  left: 0;
  border: 2px solid white;
  transform: translateX(-50px) rotate(45deg);
  transition: transform 0.5s ease;
}

.custom-button:hover {
  background: transparent !important;
  border-color: #4ED9F5 !important;
  color: #4ED9F5 !important;
}

.custom-button:hover::before {
  transform: translateX(250px) rotate(45deg);
}
</style>

