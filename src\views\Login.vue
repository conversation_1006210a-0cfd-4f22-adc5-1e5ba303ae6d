<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center relative overflow-hidden">
    <div class="background" :style="backgroundStyle"></div>
    <div class="overlay"></div>
    <div class="particles"></div>
    <!-- 主要内容容器 -->
    <div class="w-full max-w-md z-10 px-4">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-[#4ED9F5] mb-2">
          <font-awesome-icon icon="fa-shield-alt " class="text-[#4ED9F5] text-4xl mr-4" />智能车载系统安全卫士
        </h1>
        <!-- <p class="text-gray-400">大模型赋能的车载系统漏洞检测平台</p> -->
      </div>
     <!-- 切换标签 -->
      <div class="flex justify-center mb-6">
        <button 
          @click="activeTab = 'login'" 
          :class="['px-4 py-2 rounded-tl-lg rounded-bl-lg transition-all duration-300', 
                   activeTab === 'login' ? 'bg-[#4ED9F5] text-white' : 'bg-white text-gray-600 border border-gray-200']"
        >
          登录
        </button>
        <button 
          @click="goToRegister()" 
          :class="['px-4 py-2 rounded-tr-lg rounded-br-lg transition-all duration-300', 
                   activeTab === 'register' ? 'bg-[#4ED9F5] text-white' : 'bg-white text-gray-600 border border-gray-200']"
        >
          注册
        </button>
      </div>

      <el-form :model="form" :rules="rules" ref="loginForm" class="space-y-8">
        <el-form-item prop="username">
          <el-input 
            v-model="form.username" 
            placeholder="请输入用户名"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            class="custom-login-button w-full py-5 px-4 bg-[#4ED9F5] hover:bg-[#3bc8e6] transition-all duration-300"
            @click="handleLogin"
          >
            登 录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { Lock, User } from '@element-plus/icons-vue';
import axios from "axios";
import Cookies from 'js-cookie';
import qs from 'qs';

export default {
  components: {
    User,
    Lock,
  },
  data() {
    return {
      activeTab: 'login',
      form: {
        username: '',
        password: '',
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      },
      backgroundStyle: {
        transform: 'translate(0px, 0px)',
      },
    };
  },
  methods: {
    handleMouseMove(event) {
      const { clientX, clientY } = event;
      const moveX = clientX - window.innerWidth / 2;
      const moveY = clientY - window.innerHeight / 2;
      this.backgroundStyle = {
        transform: `translate(${moveX / 50}px, ${moveY / 50}px)`,
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          // console.log("请求参数:", {
          //   username: this.form.username,
          //   password: this.form.password,
          // });
          axios.post('/api/login', 
            qs.stringify({
              username: this.form.username,
              password: this.form.password
            }), 
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
              }
            }
          )
          .then(response => {
            if (response.data.msg === "success") {
              this.$message.success('登录成功');
              // console.log('接口响应:', response);
              const user = {
                username: this.form.username,
              };
              this.$store.commit('setUser', user);
              const token = response.data.data;
              Cookies.set('access_token', token, { expires: 7 });
              // this.$router.push('/dashboard');
              this.$router.push('/upload');
            } else {
              // console.log("请求失败:", response.data.msg);
              this.$message.error('登录失败，请检查用户名和密码');
            }
          })
          .catch(error => {
            // console.log("请求异常:", error);
            this.$message.error(error.response?.data?.errors || '登录失败');
          });
        }
      });
    },
    goToRegister() {
      this.$router.push('/register');
    },
  },
};
</script>

<style scoped>
:deep(.el-button) {
  border: none;
  background: #4ED9F5;
  font-weight: 500;
  color: #ffffff;
}

.custom-input :deep(.el-input__wrapper) {
  background-color: #ffffff;
  border: 1px solid #DCDFE6;
  border-radius: 1.45rem;
  height: 48px;
  line-height: 48px;
}

.custom-input :deep(.el-input__inner) {
  color: #303133;
  height: 48px;
  line-height: 48px;
  font-size: 16px;
}

.custom-input :deep(.el-input__prefix) {
  color: #4ED9F5;
  font-size: 18px;
  margin-left: 8px;
}

:deep(.el-button:hover) {
  background: #3EAEC2;
  transform: scale(1.02);
}

:deep(.el-form-item__error) {
  color: #F56C6C;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

.shake {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(78, 217, 245, 0.1), rgba(62, 174, 194, 0.1));
  z-index: 1;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, #4ED9F5 1px, transparent 1px),
    radial-gradient(circle, #4ED9F5 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  animation: moveParticles 20s linear infinite;
  opacity: 0.1;
  z-index: 2;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes moveParticles {
  0% { background-position: 0 0, 25px 25px; }
  100% { background-position: 50px 50px, 75px 75px; }
}
.custom-login-button {
  color: #ffffff;
  font-weight: 900;
}
</style>