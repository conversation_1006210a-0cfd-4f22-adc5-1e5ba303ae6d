<template>
  <div
    class="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center relative overflow-hidden"
  >
    <div class="background" :style="backgroundStyle"></div>
    <div class="overlay"></div>
    <div class="particles"></div>
    <!-- 主要内容容器 -->
    <div class="w-full max-w-md z-10 px-4">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-[#4ED9F5] mb-2">
          <font-awesome-icon
            icon="fa-shield-alt "
            class="text-[#4ED9F5] text-4xl mr-4"
          />智能车载系统安全卫士
        </h1>
        <!-- <p class="text-gray-400">大模型赋能的车载网漏洞检测平台</p> -->
      </div>

      <!-- 切换标签 -->
      <div class="flex justify-center mb-6">
        <button
          @click="goToLogin"
          :class="[
            'px-4 py-2 rounded-tl-lg rounded-bl-lg transition-all duration-300',
            activeTab === 'login'
              ? 'bg-[#4ED9F5] text-white'
              : 'bg-white text-gray-600 border border-gray-200',
          ]"
        >
          登录
        </button>
        <button
          @click="goToRegister()"
          :class="[
            'px-4 py-2 rounded-tr-lg rounded-br-lg transition-all duration-300',
            activeTab === 'register'
              ? 'bg-[#4ED9F5] text-white'
              : 'bg-white text-gray-600 border border-gray-200',
          ]"
        >
          注册
        </button>
      </div>

      <el-form
        :model="form"
        :rules="rules"
        ref="registerForm"
        class="space-y-4"
      >
        <el-form-item prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password1">
          <el-input
            v-model="form.password1"
            type="password"
            placeholder="请输入密码"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password2">
          <el-input
            v-model="form.password2"
            type="password"
            placeholder="请再次输入密码"
            class="custom-input"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="w-full py-5 px-4 bg-[#4ED9F5] hover:bg-[#3bc8e6] transition-all duration-300 custom-login-button"
            @click="handleRegister"
          >
            注 册
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>


<script>
import { ref } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";
import { ElMessage } from "element-plus";
import { Lock, Message, User } from "@element-plus/icons-vue";
import qs from "qs";

export default {
  components: {
    User,
    Message,
    Lock,
  },
  setup() {
    const router = useRouter();
    const activeTab = ref("register");
    const form = ref({
      username: "",
      email: "",
      password1: "",
      password2: "",
    });

    const backgroundStyle = ref({
      transform: "translate(0px, 0px)",
    });

    const handleMouseMove = (event) => {
      const { clientX, clientY } = event;
      const moveX = clientX - window.innerWidth / 2;
      const moveY = clientY - window.innerHeight / 2;
      backgroundStyle.value = {
        transform: `translate(${moveX / 50}px, ${moveY / 50}px)`,
      };
    };

    const handleRegister = () => {
      if (form.value.password1 !== form.value.password2) {
        ElMessage.error("两次输入的密码不一致");
        return;
      }

      // 调试信息：打印请求 URL 和请求体
      // console.log('请求 URL:', axios.defaults.baseURL + '/api/register');
      // console.log('请求数据:', {
      //   username: form.value.username,
      //   email: form.value.email,
      //   password: form.value.password1,
      //   confirmPassword: form.value.password2,
      // });

      axios
        .post(
          "/api/register",
          qs.stringify({
            username: form.value.username,
            email: form.value.email,
            password: form.value.password1,
            confirmPassword: form.value.password2,
          })
        )
        .then((response) => {
          // 调试信息：打印返回的完整响应
          // console.log('接口响应:', response);

          if (response.data.msg === "success") {
            ElMessage.success("注册成功");
            router.push("/login");
          } else {
            ElMessage.error(response.data.msg);
          }
        })
        .catch((error) => {
          // console.error('请求错误:', error);
          ElMessage.error(error.response?.data?.msg || "注册失败");
        });
    };

    const goToLogin = () => {
      router.push("/login");
    };

    return {
      form,
      backgroundStyle,
      handleMouseMove,
      handleRegister,
      goToLogin,
      activeTab,
    };
  },
};
</script>

<style scoped>
.custom-input :deep(.el-input__wrapper) {
  background-color: #ffffff;
  border: 1px solid #DCDFE6;
  border-radius: 1.45rem;
  height: 48px;
  line-height: 48px;
}

.custom-input :deep(.el-input__inner) {
  color: #303133;
  height: 48px;
  line-height: 48px;
  font-size: 16px;
}

.custom-input :deep(.el-input__prefix) {
  color: #4ED9F5;
  font-size: 18px;
  margin-left: 8px;
}

:deep(.el-button) {
  border: none;
  background: #4ED9F5;
  font-weight: 500;
  color: #ffffff;
}

:deep(.el-button:hover) {
  background: #3EAEC2;
  transform: scale(1.02);
}

:deep(.el-form-item__error) {
  color: #F56C6C;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }
  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(78, 217, 245, 0.1), rgba(62, 174, 194, 0.1));
  z-index: 1;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, #4ED9F5 1px, transparent 1px),
    radial-gradient(circle, #4ED9F5 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  animation: moveParticles 20s linear infinite;
  opacity: 0.1;
  z-index: 2;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes moveParticles {
  0% {
    background-position: 0 0, 25px 25px;
  }
  100% {
    background-position: 50px 50px, 75px 75px;
  }
}
.custom-login-button {
  color: #ffffff;
  font-weight: 900;
}

.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  padding: 20px;
}

.register-box {
  width: 100%;
  max-width: 420px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  padding: 40px;
  transition: all 0.3s ease;
}

.register-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.register-title {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  text-align: center;
  margin-bottom: 40px;
  letter-spacing: 1px;
}

.register-form {
  .el-form-item {
    margin-bottom: 25px;
  }

  .el-input__wrapper {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .el-input__wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 2px rgba(78, 217, 245, 0.2);
  }
}

.custom-register-button {
  width: 100%;
  height: 50px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
  border-radius: 8px;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(78, 217, 245, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(78, 217, 245, 0.4);
    background: linear-gradient(135deg, #3bc8e6 0%, #2ab7d5 100%);
  }

  &:active {
    transform: translateY(0);
  }
}

.login-link {
  text-align: center;
  margin-top: 20px;
  color: #606266;
  font-size: 14px;

  a {
    color: #4ED9F5;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      color: #3bc8e6;
      text-decoration: underline;
    }
  }
}

/* 响应式调整 */
@media (max-width: 480px) {
  .register-box {
    padding: 30px 20px;
  }

  .register-title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .custom-register-button {
    height: 45px;
    font-size: 16px;
  }
}
</style>

