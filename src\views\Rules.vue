<template>
  <div class="rule-info">
    <h2>规则信息</h2>
    <div class="cards">
      <el-card shadow="hover" class="card" @mouseenter="animateCards">
        <div class="card-content">
          <div class="card-title">规则总数</div>
          <div class="card-number">{{ animatedTotalRules }} 条</div>
        </div>
      </el-card>
      <el-card shadow="hover" class="card" @mouseenter="animateCards">
        <div class="card-content">
          <div class="card-title">今日新增</div>
          <div class="card-number">{{ animatedTodaysNew }} 条</div>
        </div>
      </el-card>
      <el-card shadow="hover" class="card" @mouseenter="animateCards">
        <div class="card-content">
          <div class="card-title">今日更新</div>
          <div class="card-number">{{ animatedTodaysUpdates }} 条</div>
        </div>
      </el-card>
    </div>
    <div class="content-container">
      <div class="table-header">
        <el-input
          placeholder="过滤名称"
          v-model="filterText"
          clearable
        ></el-input>
        <el-button type="primary" @click="search">检索</el-button>
        <el-button type="primary" @click="openDialog" class="add-rule"
          >添加规则</el-button
        >
      </div>
      <el-table
        :data="paginatedTableData"
        style="width: 100%"
        stripe
        border
        :default-sort="{ prop: 'id', order: 'ascending' }"
        v-loading="loadData"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          sortable
        ></el-table-column>
        <el-table-column
          prop="ruleName"
          label="名称"
          width="260"
        ></el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          width="540"
        ></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="openDetailDialog(scope.row)"
              >详情</el-button
            >
            <el-button size="small" type="danger" @click="deleteRule(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style="margin-top: 20px; text-align: center"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
      />
    </div>

    <!-- Dialog for Adding Rules -->
    <el-dialog v-model="dialogVisible" title="添加规则" width="30%">
      <el-form>
        <el-form-item label="规则名称">
          <el-input v-model="newRule.ruleName"></el-input>
        </el-form-item>
        <el-form-item label="描述信息">
          <el-input v-model="newRule.description" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <span :v-slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddRule">确定</el-button>
      </span>
    </el-dialog>

    <!-- Dialog for Rule Details -->
    <el-dialog v-model="detailDialogVisible" title="规则详情" width="30%">
      <el-form>
        <el-form-item label="规则名称">
          <el-input
            v-model="selectedRule.ruleName"
            :disabled="!isEditable"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述信息">
          <el-input
            v-model="selectedRule.description"
            type="textarea"
            :disabled="!isEditable"
          ></el-input>
        </el-form-item>
      </el-form>
      <span :v-slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="toggleEdit">{{
          isEditable ? "保存" : "修改"
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus"; // 引入 ElMessage
import "element-plus/dist/index.css";
import axios from "axios";

export default {
  setup() {
    const totalRules = ref(0);
    const todaysNew = ref(0);
    const todaysUpdates = ref(0);

    const animatedTotalRules = ref(0);
    const animatedTodaysNew = ref(0);
    const animatedTodaysUpdates = ref(0);

    const currentPage = ref(1);
    const pageSize = ref(10);
    const filterText = ref("");
    const searchQuery = ref("");

    const tableData = ref([]);
    const loadData = ref(false);
    const dialogVisible = ref(false);
    const detailDialogVisible = ref(false);
    const newRule = ref({ ruleName: "", description: "" });
    const selectedRule = ref({ ruleName: "", description: "" });
    const isEditable = ref(false);
    const totalItems = ref(0);

    const filteredTableData = computed(() => {
      return tableData.value.filter((item) =>
        searchQuery.value ? item.ruleName === searchQuery.value : true
      );
    });

    // 分页数据
    const paginatedTableData = computed(() => {
      return tableData.value.filter((item) =>
        searchQuery.value ? item.ruleName.includes(searchQuery.value) : true
      );
    });

    // 动画效果函数
    function animateValue(refValue, endValue, duration = 1500) {
      let startTimestamp = null;
      const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = timestamp - startTimestamp;
        refValue.value = Math.min(
          Math.floor((progress / duration) * endValue),
          endValue
        );
        if (progress < duration) {
          window.requestAnimationFrame(step);
        }
      };
      window.requestAnimationFrame(step);
    }

    const handlePageSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchData();
    };

    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchData();
    };

    const search = () => {
      searchQuery.value = filterText.value;
      currentPage.value = 1;
    };

    const openDialog = () => {
      dialogVisible.value = true;
    };

    const confirmAddRule = async () => {
      if (!newRule.value.ruleName) {
        ElMessage.warning("请输入规则名称");
        return;
      }
      if (!newRule.value.description) {
        ElMessage.warning("请输入描述信息");
        return;
      }
      try {
        // 向服务器发送 POST 请求添加规则
        const response = await axios.post("/rule", {
          ruleName: newRule.value.ruleName,
          description: newRule.value.description,
        });

        if (response.data.code === 1) {
          ElMessage.success("规则添加成功");
          fetchData(); // 获取最新的规则数据
          fetchTotalRules();
          fetchTodayAdded();
          fetchTodayUpdated();
          dialogVisible.value = false; // 关闭添加规则的弹框
          newRule.value = { ruleName: "", description: "" }; // 重置表单
        } else {
          ElMessage.error("添加规则失败");
        }
      } catch (error) {
        ElMessage.error("网络错误，请重试");
      }
    };

    const openDetailDialog = (rule) => {
      selectedRule.value = { ...rule }; // 复制规则以避免直接修改
      detailDialogVisible.value = true;
      isEditable.value = false; // 初始为不可编辑
    };

    const toggleEdit = async () => {
      if (isEditable.value) {
        // 保存更新
        try {
          const response = await axios.put("/rule", {
            id: selectedRule.value.id,
            ruleName: selectedRule.value.ruleName,
            description: selectedRule.value.description,
          });

          if (response.data.code === 1) {
            ElMessage.success("规则信息已更新");
            fetchData(); // 刷新规则数据
            fetchTotalRules();
            fetchTodayAdded();
            fetchTodayUpdated();
            detailDialogVisible.value = false; // 关闭详情弹框
          } else {
            ElMessage.error("更新规则失败");
          }
        } catch (error) {
          ElMessage.error("网络错误，请重试");
        }
      } else {
        isEditable.value = true; // 变为可编辑状态
      }
    };

    // 从后端获取规则总数、表格数据以及动画更新展示规则总数
    const fetchData = async () => {
      loadData.value = true;
      try {
        const response = await axios.get("/rule/list", {
          params: {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
          },
        });
        if (response.data.code === 1) {
          tableData.value = response.data.data.items;
          totalItems.value = response.data.data.total;
          // totalRules.value = response.data.data.total;
        } else {
          ElMessage.error("数据加载失败");
        }
      } catch (error) {
        ElMessage.error("网络错误");
      } finally {
        loadData.value = false;
      }
    };

    // 请求规则总数数据
    const fetchTotalRules = async () => {
      try {
        const response = await axios.get("/rule/list");
        if (response.data.code === 1) {
          totalRules.value = response.data.data.total;
          animateValue(animatedTotalRules, totalRules.value);
        } else {
          ElMessage.error("获取今日新增数据失败");
        }
      } catch (error) {
        ElMessage.error("网络错误，获取今日新增数据失败");
      }
    };

    // 请求今日新增规则数量数据
    const fetchTodayAdded = async () => {
      try {
        const response = await axios.get("/rule/todayAddedRulesCount");
        if (response.data.code === 1) {
          todaysNew.value = response.data.data;
          animateValue(animatedTodaysNew, todaysNew.value);
        } else {
          ElMessage.error("获取今日新增数据失败");
        }
      } catch (error) {
        ElMessage.error("网络错误，获取今日新增数据失败");
      }
    };

    // 请求今日更新规则数量数据
    const fetchTodayUpdated = async () => {
      try {
        const response = await axios.get("/rule/todayUpdatedRulesCount");
        if (response.data.code === 1) {
          todaysUpdates.value = response.data.data;
          animateValue(animatedTodaysUpdates, todaysUpdates.value);
        } else {
          ElMessage.error("获取今日更新数据失败");
        }
      } catch (error) {
        ElMessage.error("网络错误，获取今日更新数据失败");
      }
    };

    onMounted(() => {
      fetchData();
      fetchTotalRules();
      fetchTodayAdded();
      fetchTodayUpdated();
    });

    const deleteRule = async (row) => {
      try {
        const response = await axios.delete(`/rule/${row.id}`);
        if (response.data.code === 1) {
          ElMessage.success("删除成功");
          fetchData(); // 重新获取数据列表
        } else {
          ElMessage.error("删除失败，请稍后重试");
        }
      } catch (error) {
        ElMessage.error("删除失败");
      }
    };

    return {
      animatedTotalRules,
      animatedTodaysNew,
      animatedTodaysUpdates,
      filterText,
      search,
      paginatedTableData,
      totalItems,
      pageSize,
      currentPage,
      handlePageChange,
      handlePageSizeChange,
      dialogVisible,
      newRule,
      openDialog,
      detailDialogVisible,
      selectedRule,
      openDetailDialog,
      toggleEdit,
      isEditable,
      confirmAddRule,
      loadData,
      deleteRule,
    };
  },
};
</script>

<style scoped>
.rule-info {
  color: #fff;
}
.cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card {
  width: 32%;
  background: #ffffff;
  border: none;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s;
  border-radius: 16px;
}

.card:hover {
  transform: scale(1.05);
}

.card-content {
  text-align: left;
  padding: 20px;
}

.card-title {
  font-size: 1.2em;
  color: #000000;
}

.card-number {
  font-size: 3em;
  font-weight: bold;
  color: #4ed9f5;
  font-family: "BowlbyOne-Regular";
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.table-header > *:not(:first-child) {
  margin-left: 10px;
}

.add-rule {
  margin-left: auto;
}
.el-table {
  background-color: transparent;
}

.el-table__header,
.el-table__body {
  background-color: transparent;
}

.el-table th,
.el-table td {
  background-color: transparent;
  border: none;
}
</style>
