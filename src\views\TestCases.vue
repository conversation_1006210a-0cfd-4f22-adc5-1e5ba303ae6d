<template>
  <div class="fuzzing-test-container">
    <div class="fuzzing-test-content">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="创建用例" name="create-case">
          <CreateCase />
        </el-tab-pane>
        <el-tab-pane label="历史用例" name="case-info">
          <HistoryCase ref="historyCaseRef"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { ref ,watch, nextTick} from 'vue';
import CreateCase from '@/components/testcases/CreateCase.vue';
import HistoryCase from '@/components/testcases/HistoryCase.vue';

export default {
  components: { CreateCase, HistoryCase },
  setup() {
    const activeTab = ref('create-case');
    const historyCaseRef = ref(null);

    watch(activeTab, (newTab) => {
      if (newTab === 'case-info') {
        nextTick(() => {
          if (historyCaseRef.value && typeof historyCaseRef.value.fetchData === 'function') {
            historyCaseRef.value.fetchData();
          } else {
            console.error('fetchData 方法未暴露，请检查 HistoryCase 组件');
          }
        });
      }
    });

    return { activeTab, historyCaseRef };
  }
};
</script>

<style scoped>
::v-deep .el-tabs__item {
  font-size: 20px;
  font-weight: bold;
}
</style>
