<template>
  <div class="data-management">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="抓包配置" name="capture-config" class="capture-config-tab">
        <CaptureConfig />
      </el-tab-pane>
      <el-tab-pane label="历史数据" name="data-list" class="capture-config-tab">
        <DataList ref="dataListRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref,watch } from 'vue';
import CaptureConfig from '@/components/upload/CaptureConfig.vue';
import DataList from '@/components/upload/DataList.vue';

export default {
  components: { CaptureConfig, DataList },
//   setup() {
//     const activeTab = ref('capture-config'); // 默认展示抓包配置页面
//     return { activeTab };
//   }
    setup() {
        const activeTab = ref('capture-config'); // 默认展示抓包配置页面
        const dataListRef = ref(null); // 引用 DataList 组件

        // 监听 activeTab 的变化
        watch(activeTab, (newTab) => {
        if (newTab === 'data-list' && dataListRef.value) {
            dataListRef.value.fetchData();
        }
        });

        return { activeTab, dataListRef };
  }
};
</script>

<style scoped>
.data-management {
  height: 100%;
}
::v-deep .el-tabs__item {
  font-size: 20px;
  font-weight: bold;
}
</style>
