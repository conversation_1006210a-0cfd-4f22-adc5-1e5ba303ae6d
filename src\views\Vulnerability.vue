<template>
  <div class="vulnerability-check">
    <h2>漏洞检测</h2>
    <div class="container">
      <div class="left-panel">
        <el-card class="config-card" shadow="always">
          <div :v-slot="header" class="card-header">
            <span>配置信息</span>
          </div>
          <div class="form-item">
            <label for="api">Can 接口</label>
            <el-autocomplete
              v-model="api"
              :fetch-suggestions="querySuggestions"
              placeholder="选择或输入 can 接口"
              @select="handleSelect"
            />
          </div>

          <div class="form-item">
            <label for="testCases">测试用例</label>
             <el-select
              v-model="selectedTests"
              multiple
              filterable
              placeholder="选择测试用例"
              :loading="loadingTestCases"
            >
              <el-option
                v-for="test in testCases"
                :key="test.value"
                :label="test.label"
                :value="test.value"
              />
            </el-select>
          </div>

          <div class="buttons">
            <el-button type="primary" @click="startCheck" :loading="loading">开始检测</el-button>
            <el-button type="default" @click="stopCheck">停止检测</el-button>
          </div>

          <div class="time-display">
            <span>已进行时间: {{ formattedElapsedTime }}</span>
          </div>
        </el-card>

        <el-card class="result-card" shadow="always" style="margin-top: 20px;">
          <div :v-slot="header" class="card-header">
            <span>检测结果</span>
          </div>
          <div class="result-buttons">
            <el-button 
              @click="downloadLog" 
              type="primary" 
              :disabled="!detectionComplete">
              下载日志
            </el-button>
            <el-button 
              @click="generateReport" 
              type="primary" 
              :loading="generatingReport"
              :disabled="!detectionComplete">
              {{ reportGenerated ? '重新生成' : '生成分析报告' }}
            </el-button>
            <el-button 
              v-if="reportGenerated" 
              type="default" 
              @click="viewReport" 
              class="view-report-btn"
              style="margin-left: 10px;">
              查看报告
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <el-dialog v-model="reportDialogVisible" title="分析报告" width="50%">
      <pre>{{ reportContent }}</pre>
      <span class="dialog-buttons">
        <el-button @click="downloadReport">下载报告</el-button>
        <el-button @click="reportDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';

export default {
  name: 'VulnerabilityCheck',
  setup() {
    const api = ref('');
    const apiOptions = ref([
      { value: 'vcan0', label: 'vcan0' }, 
      // { value: 'vcan1', label: 'vcan1' }
    ]);
    const selectedTests = ref([]);
    const testCases = ref([]);
    const loadingTestCases = ref(false);
    const log = ref('');
    const loading = ref(false);
    const generatingReport = ref(false);
    const reportGenerated = ref(false);
    const reportDialogVisible = ref(false);
    const reportContent = ref('');
    const startTime = ref(0);
    const elapsedTime = ref(0);
    const detectionComplete = ref(false);
    let interval = null;

    const fetchTestCases = async () => {
      loadingTestCases.value = true;
      try {
        const params = {
          pageNum: 1,
          pageSize: 1000
        };
        const response = await axios.get('/testcase/list', { params });
        if (response.data.code === 1) {
          // 存储测试用例及其 id
          testCases.value = response.data.data.items.map(item => ({
            value: item.id,
            label: item.fileName
          }));
        } else {
          ElMessage.error('加载测试用例失败');
        }
      } catch (error) {
        ElMessage.error('请求失败');
      } finally {
        loadingTestCases.value = false;
      }
    };

    const formattedElapsedTime = computed(() => {
      const hours = Math.floor(elapsedTime.value / 3600);
      const minutes = Math.floor((elapsedTime.value % 3600) / 60);
      const seconds = elapsedTime.value % 60;
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    });

    const querySuggestions = (queryString, callback) => {
      const suggestions = apiOptions.value.filter(option => 
        option.label.toLowerCase().includes(queryString.toLowerCase())
      );
      callback(suggestions);
    };

    const handleSelect = (item) => {
      api.value = item.label;
    };

    const startCheck = async () => {
      if (!api.value || selectedTests.value.length === 0) {
        ElMessage.error('请填写 can 接口和选择测试用例');
        return;
      }

      ElMessage.success("测试开始");
      // 重置计时器
      elapsedTime.value = 0;
      startTime.value = Date.now();
      detectionComplete.value = false;
      
      // 启动计时器
      interval = setInterval(() => {
        elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000);
      }, 1000);

      try {
        const port = encodeURIComponent(api.value);
        const fileId = encodeURIComponent(selectedTests.value[0]);
        const response = await axios.post('/detect/test', null, {
          params: { port, fileId }
        });
        if (response.data.code === 1) {
          loading.value = true;
          if(response.data.data === "已完成测试"){
            ElMessage.success('检测完成');
            clearInterval(interval);
            detectionComplete.value = true;
            loading.value = false;
          }
        } else {
          ElMessage.error("测试失败");
        }
      } catch (error) {
        ElMessage.error("网络错误，请重试");
      }

    };

    const stopCheck = () => {
      clearInterval(interval);
      loading.value = false;
      ElMessage.info('检测已停止');
    };

    const generateReport = () => {
      generatingReport.value = true;
      setTimeout(() => {
        reportContent.value = "分析报告内容：\n- 漏洞识别：无\n- 风险等级：低\n- 需求覆盖率：14.1%\n- 修复建议：无\n";
        reportGenerated.value = true;
        generatingReport.value = false;
        ElMessage.success('分析报告生成完成');
      }, 3000);
    };

    const viewReport = () => {
      reportDialogVisible.value = true;
    };

    const downloadReport = () => {
      const blob = new Blob([reportContent.value], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'report.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElMessage.success('报告已下载');
    };

    const downloadLog = async () => {
      if (!api.value || selectedTests.value.length === 0) {
        ElMessage.error('请先选择 Can 接口和测试用例');
        return;
      }
      try {
        const fileId = encodeURIComponent(selectedTests.value[0]);
        const response = await axios.get('/detect/getlogfile', {
          params: { fileId: fileId }
        });
        if (response.data.code === 1) {
          const logLink = response.data.data;
          const link = document.createElement('a');
          link.href = logLink;
          link.download = 'logfile.log';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          ElMessage.success('日志正在下载...');
        } else {
          ElMessage.error('日志下载失败');
        }
      } catch (error) {
        ElMessage.error('请求日志失败');
      }
    };

    onMounted(() => {
      fetchTestCases();
    });

    return {
      api,
      apiOptions,
      selectedTests,
      testCases,
      loadingTestCases,
      log,
      loading,
      generatingReport,
      reportGenerated,
      reportDialogVisible,
      reportContent,
      elapsedTime,
      detectionComplete,
      startCheck,
      stopCheck,
      generateReport,
      viewReport,
      downloadReport,
      downloadLog,
      querySuggestions,
      handleSelect,
      formattedElapsedTime,
    };
  },
};
</script>

<style scoped>
.container {
  display: flex;
  gap: 20px;
}
.left-panel {
  flex: 1;
}
.config-card,
.result-card {
  background-color: #ffffff;
  border-radius: 8px;
}
.form-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.form-item label {
  margin-right: 10px;
  width: 100px;
}
.buttons {
  margin: 10px 0;
}
.result-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}
.time-display {
  margin-top: 10px;
}
.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.card-header {
  margin-bottom: 10px;
}

/* 添加测试用例选择框样式 */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  background-color: #409EFF !important;
  box-shadow: 0 0 0 1px #409EFF !important;
}

:deep(.el-select .el-input__inner) {
  color: #fff !important;
}

:deep(.el-select .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.7) !important;
}

:deep(.el-select .el-input__suffix) {
  color: #fff !important;
}

:deep(.el-select .el-input__suffix-inner .el-select__caret) {
  color: #fff !important;
}

:deep(.el-select-dropdown__item) {
  color: #333;
}

:deep(.el-select-dropdown__item.selected) {
  color: #409EFF;
  font-weight: bold;
}

:deep(.el-select .el-tag) {
  background-color:rgb(130, 190, 250) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color:rgb(0, 0, 0) !important;
}

/* 添加报告弹窗样式 */
:deep(.el-dialog) {
  background-color: #1a1a1a;
  color: #fff;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-size: 18px;
  font-weight: bold;
}

:deep(.el-dialog__header) {
  background-color: #2a2a2a;
  padding: 15px 20px;
  border-bottom: 1px solid #3a3a3a;
}

:deep(.el-dialog__body) {
  color: #fff;
}

:deep(.el-dialog__body pre) {
  color: #fff;
  background-color: #2a2a2a;
  padding: 15px;
  border-radius: 4px;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
