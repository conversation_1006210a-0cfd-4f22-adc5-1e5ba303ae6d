<template>
  <div class="register-container" @mousemove="handleMouseMove">
    <div class="background" :style="backgroundStyle"></div>
    <div class="overlay"></div>
    <div class="particles"></div>
    <el-card class="register-card">
      <h1 class="title">注册</h1>
      <el-form :model="form" :rules="rules" ref="registerForm" label-width="0px">
        <el-form-item prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" class="custom-input">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" class="custom-input">
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password1">
          <el-input v-model="form.password1" type="password" placeholder="请输入密码" class="custom-input">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password2">
          <el-input v-model="form.password2" type="password" placeholder="请再次输入密码" class="custom-input">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="register-button" @click="handleRegister">注册</el-button>
        </el-form-item>
        <el-form-item>
          <el-link class="login-link" @click="goToLogin">已有账号？去登录</el-link>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>


<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { Lock, Message, User } from '@element-plus/icons-vue';
import qs from 'qs';

export default {
  components: {
    User,
    Message,
    Lock,
  },
  setup() {
    const router = useRouter();
    const form = ref({
      username: '',
      email: '',
      password1: '',
      password2: '',
    });

    const backgroundStyle = ref({
      transform: 'translate(0px, 0px)',
    });

    const handleMouseMove = (event) => {
      const { clientX, clientY } = event;
      const moveX = clientX - window.innerWidth / 2;
      const moveY = clientY - window.innerHeight / 2;
      backgroundStyle.value = {
        transform: `translate(${moveX / 50}px, ${moveY / 50}px)`,
      };
    };

    const handleRegister = () => {
      if (form.value.password1 !== form.value.password2) {
        ElMessage.error('两次输入的密码不一致');
        return;
      }

      // 调试信息：打印请求 URL 和请求体
      // console.log('请求 URL:', axios.defaults.baseURL + '/api/register');
      // console.log('请求数据:', {
      //   username: form.value.username,
      //   email: form.value.email,
      //   password: form.value.password1,
      //   confirmPassword: form.value.password2,
      // });

      axios.post('/api/register', 
        qs.stringify({
          username: form.value.username,
          email: form.value.email,
          password: form.value.password1,
          confirmPassword: form.value.password2,
        }),
      )
      .then((response) => {
        // 调试信息：打印返回的完整响应
        // console.log('接口响应:', response);

        if (response.data.msg === "success") {
          ElMessage.success('注册成功');
          router.push('/login');
        } else {
          ElMessage.error(response.data.msg);
        }
      })
      .catch((error) => {
        // console.error('请求错误:', error);
        ElMessage.error(error.response?.data?.msg || '注册失败');
      });
    };

    const goToLogin = () => {
      router.push('/login');
    };

    return {
      form,
      backgroundStyle,
      handleMouseMove,
      handleRegister,
      goToLogin,
    };
  },
};
</script>

<style scoped>
.register-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  overflow: hidden;
  background-color: #000;
}

.background {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: url('/path/to/your/background-image.jpg') no-repeat center center;
  background-size: cover;
  filter: blur(5px);
  z-index: 0;
  transition: transform 0.2s ease-out;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,50,100,0.5));
  z-index: 1;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, #ffffff 1px, transparent 1px),
    radial-gradient(circle, #ffffff 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  animation: moveParticles 20s linear infinite;
  opacity: 0.3;
  z-index: 2;
}

.register-card {
  position: relative;
  width: 400px;
  padding: 30px;
  background-color: transparent;
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
  color: #ffffff;
  z-index: 3;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.register-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(180deg, #ffffff, #3498db);
  z-index: -1;
  border-radius: 18px;
}

.register-card::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background-color: rgb(2, 6, 17);
  border-radius: 14px;
  z-index: -1;
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #a0c4ff;
  font-size: 32px;
  text-shadow: 0 0 10px rgba(160, 196, 255, 0.5);
  animation: pulse 2s infinite;
}

.custom-input {
  background-color: rgba(42, 59, 71, 0.6);
  border-radius: 8px;
  margin-bottom: 20px;
}

.custom-input .el-input__inner {
  color: #ffffff;
}

.el-icon {
  color: #4caf50;
}

.register-button {
  width: 100%;
  background: linear-gradient(45deg, #00a8ff, #007bff);
  border: none;
  font-size: 18px;
  padding: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(0,150,255,0.5);
}

.register-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(0,150,255,0.8);
}

.login-link {
  display: block;
  text-align: center;
  color: #80deea;
  margin-top: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s;
}

.login-link:hover {
  color: #b2ebf2;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes moveParticles {
  0% { background-position: 0 0, 25px 25px; }
  100% { background-position: 50px 50px, 75px 75px; }
}
</style>
