<template>
  <div class="fuzzing-test-container">
    <div class="background"></div>
    <div class="overlay"></div>
    <div class="particles"></div>
    <div class="fuzzing-test-content">
      <el-tabs v-model="activeTab">
        <!-- 创建用例 -->
        <el-tab-pane label="创建用例" name="create-case">
          <el-form :model="fuzzingForm" label-width="150px">
            <el-card class="create-case-container" shadow="always">
              <el-form-item label="规则及数据配置" class="form-item">
                <el-select v-model="fuzzingForm.rule" placeholder="请选择规则" multiple filterable>
                  <el-option
                    v-for="ruleName in ruleNames" 
                    :key="ruleName" 
                    :label="ruleName" 
                    :value="ruleName"
                  />
                </el-select>
                <el-select v-model="fuzzingForm.data" placeholder="请选择数据" multiple filterable>
                  <el-option
                    v-for="fileName in fileNames" 
                    :key="fileName" 
                    :label="fileName" 
                    :value="fileName"
                    :loading="loadingFileNames"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="大模型" class="form-item">
                <el-radio-group v-model="fuzzingForm.model">
                  <el-radio-button label="Kimi"></el-radio-button>
                  <el-radio-button label="文心一言"></el-radio-button>
                  <el-radio-button label="通义千问"></el-radio-button>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="生成测试用例个数" class="form-item">
                <el-input-number v-model="fuzzingForm.testCaseCount" :min="1" :max="1000" label="生成测试用例个数"></el-input-number>
              </el-form-item>

              <el-form-item label="描述" class="form-item">
                <el-input type="textarea" v-model="fuzzingForm.description" placeholder="请输入描述" rows="3" autosize></el-input>
              </el-form-item>

              <el-form-item class="form-item-button">
                <el-button type="primary" @click="startTest">创建用例</el-button>
                <el-button type="primary" @click="clearOptions">清空选项</el-button>
              </el-form-item>
            </el-card>

            <el-progress :percentage="progress" :stroke-width="5" :show-text="false" class="custom-progress" />
            <el-table ref="taskTable" :data="tasks" :row-class-name="tableRowClassName" style="margin-top: 20px;" height="350" stripe border>
              <el-table-column prop="id" label="用例ID" width="100" sortable />
              <el-table-column prop="fileName" label="用例名称" />
              <el-table-column prop="description" label="描述" />
              <el-table-column label="链接" prop="filePath" width="550">
                  <template #default="{ row }">
                    <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{ row.filePath }}</a>
                  </template>
                </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button size="small" type="danger" @click="confirmDeleteCase(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-tab-pane>

        <!-- 历史用例 -->
        <el-tab-pane label="历史用例" name="case-info">
          <div class="data-table">
            <div class="buttons">
              <el-button type="primary" @click="triggerUpload">上传用例</el-button>
              <el-button type="danger" @click="confirmBatchDelete" :disabled="!selectedRows.length">批量删除</el-button>
              <input ref="fileInput" type="file" @change="uploadData" style="display: none" />
            </div>

            <el-table
              :data="paginatedData"
              stripe
              border
              v-loading="loading"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="用例ID" width="100" sortable></el-table-column>
              
              <!-- 文件名列：点击后变为可编辑 -->
              <el-table-column label="文件名" width="150" prop="fileName">
                <template #default="scope">
                  <div v-if="editingId !== scope.row.id" @click="startEditing(scope.row)">
                    {{ scope.row.fileName }}
                  </div>
                  <el-input
                    v-else
                    v-model="editingName"
                    @blur="finishEditing(scope.row)"
                    placeholder="编辑文件名"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" width="200"></el-table-column>
              <!-- 链接列，点击后开始下载 -->
              <el-table-column label="链接" prop="filePath" width="550">
                  <template #default="{ row }">
                    <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{ row.filePath }}</a>
                  </template>
                </el-table-column>

              <el-table-column label="操作">
                <template #default="scope">             
                  <el-button size="small" type="danger" @click="confirmDeleteCase(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination
              style="margin-top: 20px; text-align: center;"
              @size-change="handlePageSizeChange"
              @current-change="handlePageChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 上传用例对话框 -->
    <el-dialog
      title="上传用例"
      v-model="uploadDialogVisible"
      width="30%"
      @close="resetUploadDialog"
    >
      <el-form :model="uploadForm" label-width="80px">
        <!-- 文件选择 -->
        <el-form-item label="文件">
          <el-upload
            ref="fileUploader"
            :before-upload="validateFile"
            :on-change="handleFileChange"
            :file-list="fileList"
            :limit="1"
            accept=".xml"
            action=""
            :auto-upload="false"
          >
            <el-button>
              选择文件
            </el-button>
            <!-- <div :v-slot="tip" class="el-upload__tip">仅支持 .xml 文件</div> -->
          </el-upload>
        </el-form-item>

        <!-- 描述输入框 -->
        <el-form-item label="描述">
          <el-input
            type="textarea"
            v-model="uploadForm.description"
            placeholder="请输入描述（可选）"
            rows="3"
          ></el-input>
        </el-form-item>
      </el-form>

      <div :v-slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitFile">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
export default {
  data() {
    return {
      activeTab: 'create-case',
      fuzzingForm: {
        rule: [],
        data: [],
        model: 'Kimi',
        testCaseCount: 1,
        description: ''
      },
      tasks: [],
      progress: 0,
      caseData: [], // 初始化为空数组，防止 length 报错
      loading: false,
      selectedRows: [],
      currentPage: 1,
      pageSize: 10,
      editingId:null,
      editingName:'',
      uploadDialogVisible: false, // 控制上传对话框的显示
      uploadForm: {
        file: null, // 存储选中的文件
        description: "", // 用户填写的描述
      },
      fileList: [], // 文件列表（用于展示上传组件）
      loadingFileNames: false,
      fileNames: [],  // 存储文件名
      ruleNames: []   // 存储规则名称
    };
  },
  created() {
    this.fetchFileNames();
    this.fetchRuleNames();  // 获取规则名称
  },
  computed: {
    paginatedData() {
      // const start = (this.currentPage - 1) * this.pageSize;
      // return Array.isArray(this.caseData) ? this.caseData.slice(start, start + this.pageSize) : [];
      return this.caseData;
    },
    total() {
      return Array.isArray(this.caseData) ? this.caseData.length : 0;
    }
  },
  methods: {
    async fetchFileNames() {
      this.loadingFileNames = true;
        try {
          const response = await axios.get('/original/list');  // 向接口发送 GET 请求
          if (response.data.code === 1) {
            this.fileNames = response.data.data.items.map(item => item.fileName);  // 提取 fileName
          } else {
            ElMessage.error('加载文件名失败');
          }
        } catch (error) {
          ElMessage.error('请求失败');
          // console.error(error);
        }finally {
          this.loadingFileNames = false;
      }
    },

    async fetchRuleNames() {
      this.loadingFileNames = true;
      try {
        const response = await axios.get('/rule');  // 获取规则列表的接口
        if (response.data.code === 1) {
          this.ruleNames = response.data.data.map(data => data.ruleName);  // 提取规则名称
        } else {
          ElMessage.error('加载规则失败');
        }
      } catch (error) {
        ElMessage.error('请求失败');
        // console.error(error);
      }finally {
        this.loadingFileNames = false;
      }
    },
    startTest() {
      // 验证规则和数据
      if (!this.fuzzingForm.rule.length || !this.fuzzingForm.data.length) {
        this.$message.error('请先选择规则和数据');
        return;
      }

      if (!this.fuzzingForm.testCaseCount || this.fuzzingForm.testCaseCount < 1) {
        this.$message.error('请设置生成测试用例个数');
        return;
      }

      // this.tasks = []; // 重置任务列表
      this.progress = 0;

      this.$message.success(`测试已开始，模型 ${this.fuzzingForm.model}`);

    
      // 生成一条用例数据
      const newTask = { 
        id: this.tasks.length + 1, // 用例ID
        fileName: `用例 ${this.tasks.length + 1}`, // 用例名称
        description: this.fuzzingForm.description // 描述
      };

      this.tasks.push(newTask);
      this.progress = 100; // 设置进度为100%
      this.$message.success('测试完成');
    },

    clearOptions() {
      this.fuzzingForm.rule = [];
      this.fuzzingForm.data = [];
      this.fuzzingForm.model = 'Kimi';
      this.fuzzingForm.testCaseCount = 1;
      this.fuzzingForm.description = ''; // 清空描述
      this.$message.info('选项已清空');
    },
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
    async fetchData() {
      this.loading = true;
      try {
        const response = await axios.get('/testcase/list', {
          params: {
            pageNum: this.currentPage,
            pageSize: this.pageSize
          }
        });
        this.caseData = response.data.data.items || []; 
      } catch (error) {
        ElMessage.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchData();
    },
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchData();
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },

    startEditing(row){
      this.editingId = row.id;
      this.editingName = row.fileName;
    },
    async finishEditing(row){
      const { id, fileName: originalFileName } = row; // 解构行数据
      const newFileName = this.editingName.trim();  // 去掉输入的多余空格

      // 如果文件名未修改，直接返回
      if (newFileName === originalFileName) {
        this.editingId = null;
        return;
      }

      // 检查文件名是否重复
      const isDuplicate = this.caseData.some(data => data.fileName === newFileName && data.id !== id);
      if (isDuplicate) {
        ElMessage.error('文件名已存在，请选择不同的名称');
        this.editingName = originalFileName; // 恢复原始名称
        this.editingId = null;
        return;
      }

      try {
        // 准备请求数据
        const payload = { id, fileName: newFileName };
        // 发送修改请求
        const response = await axios.put('/testcase', payload);

        // 处理响应
        if (response.data.msg === "success") {
          ElMessage.success('文件名修改成功');
          row.fileName = newFileName; // 更新行数据
        } else {
          ElMessage.error('文件名修改失败，请稍后重试');
          // console.error('文件名修改失败:', response.data);
        }
      } catch (error) {
        // console.error('文件名修改请求错误:', error);
        ElMessage.error('文件名修改失败');
      } finally {
        this.editingId = null; // 停止编辑模式
      }
    },
    async deleteFile(row) {
      // tableData.value = tableData.value.filter(data => data.id !== row.id);
      try {
        // 调用批量删除接口
        const response = await axios.delete(`/testcase/${row.id}`);

        if (response.data.code === 1) {
          // 成功后更新本地数据
          // tableData.value = tableData.value.filter(data => !selectedRows.value.some(row => row.id === data.id));
          ElMessage.success('删除成功');
          this.fetchData(); // 重新获取数据列表
        } else {
          ElMessage.error('删除失败，请稍后重试');
          // console.error('删除请求失败:', response.data);
        }
      } catch (error) {
        // console.error('删除请求错误:', error);
        // console.log(error.response.data);
        ElMessage.error('删除失败');
      }
      
    },
    async confirmBatchDelete() {
      if (!this.selectedRows.length) {
        ElMessage.warning('请选择要删除的用例');
        return;
      }
      try {
        await ElMessageBox.confirm(
          '此操作将永久删除选中的用例，是否继续？',
          '警告',
          { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
        );
        this.batchDelete();
      } catch {
        ElMessage.info('取消删除');
      }
    },
    async batchDelete() {
      const ids = this.selectedRows.map(row => row.id).join(',');
      try {
        await axios.delete(`/testcase/${ids}`);
        this.caseData = this.caseData.filter(row => !this.selectedRows.includes(row));
        ElMessage.success('批量删除成功');
        this.fetchData();
      } catch {
        ElMessage.error('批量删除失败');
      }
    },
 

    // 打开上传用例对话框
    triggerUpload() {
      this.uploadDialogVisible = true;
    },

    // 校验文件类型
    validateFile(file) {
      const isXml = file.type === "text/xml";
      if (!isXml) {
        ElMessage.error("只能上传 XML 文件！");
      }
      return isXml;
    },

    // 处理文件变化事件
    handleFileChange(file) {
      this.uploadForm.file = file.raw; // 获取原始文件对象
    },

    // 提交文件和描述
    async submitFile() {
      
      // 获取用户选择的文件
      const file = this.uploadForm.file;
      if (!file) {
        ElMessage.error('请先选择文件');
        return;
      }

      // 获取文件名
      const fileName = file.name;
      // 获取描述
      const description = this.uploadForm.description;

      // 准备 FormData 用于文件上传
      const formData = new FormData();
      formData.append('file', file);

      try {
        // 发送文件上传请求
        const uploadResponse = await axios.post('/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        // 检查上传结果
        if (uploadResponse.data.msg === 'success') {
          // 上传成功
          ElMessage.success('文件已上传到云服务器');
          
          // 获取上传文件的路径
          const filePath = uploadResponse.data.data;  

          // 准备保存请求的参数
          const saveRequestData = {
            fileName: fileName,
            filePath: filePath,
            description: description,
          };

          // 输出保存请求的参数
          // console.log("保存请求的参数: ", saveRequestData);

          // 发送文件信息保存请求
          const saveResponse = await axios.post('/testcase', saveRequestData);

          // 检查保存结果
          if (saveResponse.data.msg === 'success') {
            // 保存成功
            ElMessage.success('文件信息已存储到数据库');
            // 关闭上传对话框
            this.uploadDialogVisible = false;
            // 重置表单数据
            this.uploadForm.file = null;
            this.uploadForm.description = "";
            this.fileList = [];

            this.fetchData(); // 重新获取数据列表
          } else {
            // 保存失败
            ElMessage.error('文件信息存储失败');
          }
        } else {
          // 上传失败
          ElMessage.error('文件上传失败');
        }
      } catch (error) {
        // 捕获错误
        // console.error('上传失败:', error);
        ElMessage.error('文件上传过程中发生错误');
      }
    },

    // 重置上传对话框
    resetUploadDialog() {
      this.uploadForm.file = null;
      this.uploadForm.description = "";
      this.fileList = [];
    },

    async confirmDeleteCase(row) {
      try {
        await ElMessageBox.confirm(`确认删除 ${row.fileName} 吗？`, '删除确认', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' });
        await this.deleteCase(row);
      } catch {
        ElMessage.info('取消删除');
      }
    },
    async deleteCase(row) {
      await axios.delete(`/testcase/${row.id}`);
      this.caseData = this.caseData.filter(item => item.id !== row.id);
      ElMessage.success('删除成功');
      this.fetchData();
    },
  },
  mounted() {
    this.fetchData();
  }
};
</script>


<style scoped>
.form-item {
  margin-bottom: 15px;
}
.buttons {
  margin-bottom: 20px;
  text-align: right;
}
.data-table {
  margin-top: 20px;
}
::v-deep .el-tabs__item {
  font-size: 20px;
  font-weight: bold;
}
.el-select {
  width: 40%;
  margin-right: 15px;
}
.create-case-container{
  margin-bottom: 10px;
  background-color: #16171A;
  border-radius: 8px;
}
</style>
