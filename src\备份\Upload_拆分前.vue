<template>
  <div class="data-management">
    <el-tabs v-model="activeTab">
      <!-- 抓包配置页面 -->
      <el-tab-pane label="抓包配置" name="capture-config">
        <div class="capture-config">
          <el-card class="capture-config-form">
            <el-form>
              <el-form-item label="Can接口">
                <el-autocomplete
                  v-model="api"
                  :fetch-suggestions="querySuggestions"
                  placeholder="选择或输入 can 接口"
                  @select="handleSelect"
                />
              </el-form-item>

              <!-- 抓包时长 -->
              <el-form-item label="抓包时长（秒）">
                <el-input-number v-model="captureDuration" :min="1" placeholder="设置抓包时长"></el-input-number>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="startCapture">开始抓包</el-button>
                <el-button @click="stopCapture">停止抓包</el-button>
                <!-- 环形进度条 -->
                <el-progress
                  type="circle"
                  :percentage="captureProgress"
                  v-if="isCapturing"
                  :format="formatProgress"
                  width="25"
                  stroke-width="3"
                  style="margin-left: 15px"
                />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 表格：抓包起止时间、抓包时长、Can接口、操作 -->
          <el-table
            :data="capturedData"
            class="capture-table"
            stripe
            border
            height="400"
          >
            <el-table-column prop="startTime" label="抓包起止时间" width="220">
              <template #default="scope">
                <div>{{ scope.row.startTime }} - {{ scope.row.endTime }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="抓包时长（秒）" width="140"></el-table-column>
            <el-table-column prop="api" label="Can接口" width="100"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="downloadCapture(scope.row)">下载</el-button>
                <el-button size="small" type="danger" @click="deleteCapture(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 历史数据页面：增加批量删除功能 -->
      <el-tab-pane label="历史数据" name="data-list">
        <div class="data-table">
          <div class="buttons">
            <el-button type="primary" @click="syncData">同步</el-button>
            <el-button type="primary" @click="triggerUpload">上传数据</el-button>
            <el-button type="danger" @click="confirmBatchDelete" :disabled="!selectedRows.length">批量删除</el-button>
            <input ref="fileInput" type="file" @change="uploadData" style="display: none" />
          </div>

          <el-table
            :data="paginatedData"
            style="width: 100%"
            stripe
            border
            v-loading="loading"
            class="data-table"
            @selection-change="handleSelectionChange"
          >
            <!-- 添加多选框列 -->
            <el-table-column type="selection" width="55"></el-table-column>
            
            <el-table-column prop="id" label="ID" width="80" sortable></el-table-column>

            <!-- 文件名列：点击后变为可编辑 -->
            <el-table-column label="文件名" width="250" prop="fileName">
              <template #default="scope">
                <div v-if="editingId !== scope.row.id" @click="startEditing(scope.row)">
                  {{ scope.row.fileName }}
                </div>
                <el-input
                  v-else
                  v-model="editingName"
                  @blur="finishEditing(scope.row)"
                  placeholder="编辑文件名"
                />
              </template>
            </el-table-column>

            <!-- 链接列，点击后开始下载 -->
           <el-table-column label="链接" prop="filePath" width="550">
              <template #default="{ row }">
                <a :href="row.filePath" target="_blank" rel="noopener noreferrer">{{ row.filePath }}</a>
              </template>
            </el-table-column>


            <!-- 操作列：仅保留删除按钮 -->
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" type="danger" @click="deleteFile(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            style="margin-top: 20px; text-align: center;"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

export default {
  setup() {
    const activeTab = ref('capture-config'); // 默认展示抓包配置页面

    // 抓包配置相关数据
    const api = ref('');
    const apiOptions = ref([{ value: 'vcan0', label: 'vcan0' }, { value: 'vcan1', label: 'vcan1' }]);
    const captureDuration = ref(5); // 默认抓包时长为5秒
    const capturedData = ref([]);
    const isCapturing = ref(false);
    const captureProgress = ref(0);
    let captureTimeout = null;
    let captureStartTime = null;
    let captureInterval = null;

    // 开始抓包
    const startCapture = () => {
      if (!api.value) {
        ElMessage.error('请填写 can 接口');
        return;
      }
      if (captureTimeout) clearTimeout(captureTimeout);
      if (captureInterval) clearInterval(captureInterval);

      captureStartTime = new Date();
      isCapturing.value = true;
      captureProgress.value = 0;
      ElMessage.success('开始抓包');

      // 更新进度条的计时器
      captureInterval = setInterval(updateProgress, 100);

      // 在指定的抓包时长后停止抓包
      captureTimeout = setTimeout(() => {
        stopCapture();
      }, captureDuration.value * 1000);
    };

    // 停止抓包
    const stopCapture = () => {
      if (captureTimeout) clearTimeout(captureTimeout);
      if (captureInterval) clearInterval(captureInterval);

      const endTime = new Date();
      const duration = Math.round((endTime - captureStartTime) / 1000);
      capturedData.value.push({
        startTime: captureStartTime.toLocaleTimeString(),
        endTime: endTime.toLocaleTimeString(),
        duration,
        api: api.value
      });

      isCapturing.value = false;
      captureProgress.value = 100;
      ElMessage.success('抓包完成');
    };

    // 更新进度条
    const updateProgress = () => {
      const elapsed = (new Date() - captureStartTime) / 1000;
      captureProgress.value = Math.min((elapsed / captureDuration.value) * 100, 100);
    };

    // 格式化进度条的显示内容
    const formatProgress = () => {
      return `${Math.round(captureProgress.value)}%`;
    };

    // 下载抓包数据
    const downloadCapture = (row) => {
      const captureContent = `
<capture>
  <startTime>${row.startTime}</startTime>
  <endTime>${row.endTime}</endTime>
  <duration>${row.duration}</duration>
  <api>${row.api}</api>
</capture>`;
      const blob = new Blob([captureContent], { type: 'application/xml' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `抓包_${row.startTime}.xml`;
      link.click();
      URL.revokeObjectURL(url);
    };

    // 删除抓包数据
    const deleteCapture = (row) => {
      capturedData.value = capturedData.value.filter(data => data.startTime !== row.startTime);
      ElMessage.success('删除成功');
    };

    const handleSelect = (item) => {
      api.value = item.label;
    };

    const querySuggestions = (queryString, callback) => {
      const suggestions = apiOptions.value.filter(option =>
        option.label.toLowerCase().includes(queryString.toLowerCase())
      );
      callback(suggestions);
    };

    // 历史数据页面相关数据
    const tableData = ref([]);
    const total = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const loading = ref(false);

    // 分页后的数据
    // const paginatedData = computed(() => {
    //   if (Array.isArray(tableData.value)) {
    //     const start = (currentPage.value - 1) * pageSize.value;
    //     return tableData.value.slice(start, start + pageSize.value);
    //   }
    //   return []; // 如果 tableData 不是数组，返回一个空数组
    // });
    const paginatedData = computed(() => tableData.value);


    // 获取原始文件列表数据
    const fetchData = async () => {
      loading.value = true;
      const params = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      };

      // 打印请求参数用于调试
      // console.log('发送请求：查询原始文件列表', params);

      try {
        const response = await axios.get('/original/list', { params });
        // // 检查请求的配置信息（包括请求头）
        // console.log('请求头信息：', response.config.headers);
        // 打印响应结果用于调试
        // console.log('接口响应：', response.data);

        tableData.value = response.data.data.items; // 假设返回的数据列表在 response.data.items 中
        total.value = response.data.data.total;     // 假设返回的总数据条数在 response.data.total 中

        ElMessage.success('数据加载成功');
      } catch (error) {
        // console.error('请求错误：', error);
        ElMessage.error('数据加载失败');
      } finally {
        loading.value = false;
      }
    };

    // 监听 activeTab，当切换到 "data-list" 时请求数据
    watch(activeTab, (newTab) => {
      if (newTab === 'data-list') {
        fetchData();
      }
    });

    // 每页条数改变时重新请求数据
    const handlePageSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchData();
     
    };

    // 当前页码改变时重新请求数据
    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchData();
    };

    // 同步数据
    const syncData = () => {
      ElMessage.success('同步成功');
    };

    // 触发上传
    const triggerUpload = () => {
      const fileInput = document.querySelector('input[type="file"]');
      fileInput.setAttribute('accept', '.xml');
      fileInput.click();
    };

    // 上传数据
    const uploadData = async (event) => {
      const file = event.target.files[0];
      if (!file) {
        ElMessage.warning('请选择要上传的文件');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      try {
        // 第一步：上传文件
        const uploadResponse = await axios.post('/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        // 打印调试信息
        // console.log('上传成功，服务器响应:', uploadResponse.data);

        ElMessage.success('文件上传成功');

        // 假设服务器返回的上传路径和文件名在 uploadResponse.data.filePath 和 uploadResponse.data.fileName 中
        const filePath = uploadResponse.data.data;
        const fileName = file.name;

        // 第二步：发送文件信息到数据库
        const saveResponse = await axios.post('/original', { fileName, filePath });

        // 检查响应信息，给出提示
        if (saveResponse.data.msg === "success") {
          // ElMessage.success('文件信息已存储到数据库');
          //  console.log({ fileName, filePath });
          fetchData(); // 重新获取数据列表
        } else {
          ElMessage.warning('文件上传成功，但文件信息未存储到数据库');
          // console.log('文件信息未存储，服务器响应:', saveResponse.data);
        }
      } catch (error) {
        // console.error('操作失败:', error);
        ElMessage.error(error.response?.data?.message || '文件上传失败');
      }
    };

    // 删除文件
    const deleteFile = async (row) => {
      // tableData.value = tableData.value.filter(data => data.id !== row.id);
      try {
        // 调用批量删除接口
        const response = await axios.delete(`/original/${row.id}`);

        if (response.data.code === 1) {
          // 成功后更新本地数据
          // tableData.value = tableData.value.filter(data => !selectedRows.value.some(row => row.id === data.id));
          ElMessage.success('删除成功');
          fetchData(); // 重新获取数据列表
        } else {
          ElMessage.error('删除失败，请稍后重试');
          // console.error('删除请求失败:', response.data);
        }
      } catch (error) {
        // console.error('删除请求错误:', error);
        // console.log(error.response.data);
        ElMessage.error('删除失败');
      }
      
    };

    const editingId = ref(null);
    const editingName = ref('');

    const startEditing = (row) => {
      editingId.value = row.id;
      editingName.value = row.fileName;
    };

    const finishEditing = async (row) => {
      const { id, fileName: originalFileName } = row; // 解构行数据
      const newFileName = editingName.value.trim();  // 去掉输入的多余空格

      // 如果文件名未修改，直接返回
      if (newFileName === originalFileName) {
        editingId.value = null;
        return;
      }

      // 检查文件名是否重复
      const isDuplicate = tableData.value.some(data => data.fileName === newFileName && data.id !== id);
      if (isDuplicate) {
        ElMessage.error('文件名已存在，请选择不同的名称');
        editingName.value = originalFileName; // 恢复原始名称
        editingId.value = null;
        return;
      }

      try {
        // 准备请求数据
        const payload = { id, fileName: newFileName };
        // 发送修改请求
        const response = await axios.put('/original', payload);

        // 处理响应
        if (response.data.msg === "success") {
          ElMessage.success('文件名修改成功');
          row.fileName = newFileName; // 更新行数据
        } else {
          ElMessage.error('文件名修改失败，请稍后重试');
          // console.error('文件名修改失败:', response.data);
        }
      } catch (error) {
        // console.error('文件名修改请求错误:', error);
        ElMessage.error('文件名修改失败');
      } finally {
        editingId.value = null; // 停止编辑模式
      }
    };

    // 选中的行数据
    const selectedRows = ref([]);

    // 处理多选操作
    const handleSelectionChange = (rows) => {
      selectedRows.value = rows;
    };

    // 确认批量删除
    // const confirmBatchDelete = () => {
    //   ElMessageBox.confirm(
    //     '此操作将永久删除选中的文件，是否继续？',
    //     '警告',
    //     {
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'warning',
    //     }
    //   ).then(() => {
    //     batchDelete();
    //   }).catch(() => {
    //     ElMessage.info('已取消删除');
    //   });
    // };
    // 确认批量删除
    const confirmBatchDelete = () => {
      ElMessageBox.confirm(
        `此操作将永久删除选中的 ${selectedRows.value.length} 个文件，是否继续？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        batchDelete(); // 调用批量删除函数
      }).catch(() => {
        ElMessage.info('已取消删除');
      });
    };


    // 批量删除选中的文件
    // const batchDelete = () => {
    //   if (selectedRows.value.length === 0) {
    //     ElMessage.warning('请选择要删除的文件');
    //     return;
    //   }
    //   selectedRows.value.forEach(row => {
    //     tableData.value = tableData.value.filter(data => data.id !== row.id);
    //   });
    //   ElMessage.success('批量删除成功');
    //   selectedRows.value = [];
    // };
    // 批量删除选中的文件
    const batchDelete = async () => {
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请选择要删除的文件');
        return;
      }

      // 收集选中行的 ID 并拼接成逗号分隔的字符串
      const ids = selectedRows.value.map(row => row.id).join(',');

      try {
        // 调用批量删除接口
        const response = await axios.delete(`/original/${ids}`);

        if (response.data.msg === "success") {
          // 成功后更新本地数据
          tableData.value = tableData.value.filter(data => !selectedRows.value.some(row => row.id === data.id));
          ElMessage.success('批量删除成功');
        } else {
          ElMessage.error('批量删除失败，请稍后重试');
          // console.error('批量删除请求失败:', response.data);
        }
      } catch (error) {
        // console.error('批量删除请求错误:', error);
        ElMessage.error('批量删除失败');
      } finally {
        // 清空选中行
        selectedRows.value = [];
      }
    };


    
    return {
      // 活动标签
      activeTab,
      // 抓包配置相关
      api,
      apiOptions,
      captureDuration,
      capturedData,
      isCapturing,
      captureProgress,
      startCapture,
      stopCapture,
      updateProgress,
      formatProgress,
      downloadCapture,
      deleteCapture,
      querySuggestions,
      handleSelect,
      // 历史数据相关
      paginatedData,
      total,
      currentPage,
      pageSize,
      loading,
      syncData,
      triggerUpload,
      uploadData,
      deleteFile,
      handlePageSizeChange,
      handlePageChange,
      editingId,
      editingName,
      startEditing,
      finishEditing,
      selectedRows,
      handleSelectionChange,
      confirmBatchDelete,
      batchDelete,
    };
  }
};


</script>

<style scoped>
.data-management {
  height: 100%;
}

.capture-config {
  margin-bottom: 20px;
}

.capture-table {
  margin-top: 20px;
}
.buttons {
  margin-bottom: 20px;
  text-align: right;
}

::v-deep .el-tabs__item {
  font-size: 20px;
  font-weight: bold;
}
.capture-config-form {
  background-color: #16171A;
  border-radius: 8px;
  margin-bottom: 10px;
}
</style>
