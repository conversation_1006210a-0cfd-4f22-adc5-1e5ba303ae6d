<template>
  <div class="vulnerability-check">
    <h2>漏洞检测</h2>
    <div class="container">
      <div class="left-panel">
        <el-card class="config-card" shadow="always">
          <div :v-slot="header" class="card-header">
            <span>配置信息</span>
          </div>
          <div class="form-item">
            <label for="api">Can 接口</label>
            <el-autocomplete
              v-model="api"
              :fetch-suggestions="querySuggestions"
              placeholder="选择或输入 can 接口"
              @select="handleSelect"
            />
          </div>

          <div class="form-item">
            <label for="testCases">测试用例</label>
             <el-select
              v-model="selectedTests"
              multiple
              filterable
              placeholder="选择测试用例"
              :loading="loadingTestCases"
            >
              <el-option
                v-for="test in testCases"
                :key="test.value"
                :label="test.label"
                :value="test.value"
              />
            </el-select>
          </div>

          <div class="buttons">
            <el-button type="primary" @click="startCheck" :loading="loading">开始检测</el-button>
            <el-button type="default" @click="stopCheck">停止检测</el-button>
          </div>

          <div class="time-display">
            <span>已进行时间: {{ formattedElapsedTime }}</span>
          </div>
        </el-card>

        <el-card class="result-card" shadow="always" style="margin-top: 20px;">
            <div :v-slot="header" class="card-header">
                <span>检测结果</span>
            </div>
                <div class="result-buttons">
                <el-button @click="downloadLog" type="primary">下载日志</el-button>
                <el-button 
                    @click="generateReport" 
                    type="primary" 
                    :loading="generatingReport">
                    {{ reportGenerated ? '重新生成' : '生成分析报告' }}
                </el-button>
                <el-button 
                    v-if="reportGenerated" 
                    type="default" 
                    @click="viewReport" 
                    style="margin-left: 10px;">
                    查看报告
                </el-button>
                </div>
        
            <div class="log-output">
                <pre>{{ log }}</pre>
            </div>
        </el-card>

      </div>
    </div>

    <el-dialog v-model="reportDialogVisible" title="分析报告" width="50%">
      <pre>{{ reportContent }}</pre>
      <span class="dialog-buttons">
        <el-button @click="downloadReport">下载报告</el-button>
        <el-button @click="reportDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted ,computed } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';

export default {
  name: 'VulnerabilityCheck',
  setup() {
    const api = ref('');
    const apiOptions = ref([{ value: 'vcan0', label: 'vcan0' }, { value: 'vcan1', label: 'vcan1' }]);
    const selectedTests = ref([]);
    const testCases = ref([]);
    const loadingTestCases = ref(false);  // 控制测试用例下拉框的加载状态
    const log = ref('');
    const loading = ref(false);
    const generatingReport = ref(false);
    const reportGenerated = ref(false);
    const reportDialogVisible = ref(false);
    const reportContent = ref('');
    const startTime = ref(0);
    const elapsedTime = ref(0);
    let interval = null;

    // 获取测试用例文件名
    const fetchTestCases = async () => {
      loadingTestCases.value = true;  // 开始加载，显示下拉框的加载状态
      try {
        const response = await axios.get('/testcase/list'); 
        if (response.data.code === 1) {
          testCases.value = response.data.data.items.map(item => ({
            value: item.fileName,
            label: item.fileName // 假设返回的文件信息中有 fileName 字段
          }));
        } else {
          ElMessage.error('加载测试用例失败');
        }
      } catch (error) {
        ElMessage.error('请求失败');
        // console.error(error);
      } finally {
        loadingTestCases.value = false;  // 请求完成后隐藏加载状态
      }
    };

    // 计算属性，用于格式化已进行时间
    const formattedElapsedTime = computed(() => {
      const hours = Math.floor(elapsedTime.value / 3600);
      const minutes = Math.floor((elapsedTime.value % 3600) / 60);
      const seconds = elapsedTime.value % 60;
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    });

    const querySuggestions = (queryString, callback) => {
      const suggestions = apiOptions.value.filter(option => 
        option.label.toLowerCase().includes(queryString.toLowerCase())
      );
      callback(suggestions);
    };

    const handleSelect = (item) => {
      api.value = item.label;
    };

    const startCheck = () => {
        if (!api.value || selectedTests.value.length === 0) {
            ElMessage.error('请填写 can 接口和选择测试用例');
            return;
        }
        loading.value = true;
        startTime.value = Date.now();
        elapsedTime.value = 0; // Reset elapsed time on start
        interval = setInterval(() => {
            elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000);
        }, 1000);

        // 记录开始时间
        // const detectionStartTime = formattedElapsedTime.value;

        // 模拟检测过程
        const logMessages = [
            `[INFO] 检测开始...`,
            `[INFO] ${new Date().toISOString()} - 检测完成！`,
            `[INFO] ${new Date().toISOString()} - 成功识别漏洞: XSS, SQL注入.`,
            // `[INFO] ${new Date().toISOString()} - 检测时间: ${detectionStartTime}.`, // 使用记录的时间
            `[INFO] ${new Date().toISOString()} - 漏洞修复建议：请验证用户输入.`,
            `[INFO] ${new Date().toISOString()} - 系统性能未受影响.`,
            `[INFO] ${new Date().toISOString()} - 日志记录完毕.`
        ];

        let logIndex = 0;
        
        const outputLog = () => {
            if (logIndex < logMessages.length) {
            log.value += logMessages[logIndex] + '\n';
            logIndex++;
            setTimeout(outputLog, 2000); // 每行输出间隔 2 秒
            } else {
            loading.value = false;
            clearInterval(interval); // Stop interval
            ElMessage.success('检测完成');
            }
        };

        outputLog(); // 开始逐行输出日志
    };



    const stopCheck = () => {
      clearInterval(interval);
      loading.value = false;
      ElMessage.info('检测已停止');
    };

    const generateReport = () => {
      generatingReport.value = true;
      // Simulate report generation
      setTimeout(() => {
        reportContent.value = "分析报告内容：\n- 漏洞识别：XSS\n- 风险等级：高\n- 修复建议：验证用户输入\n";
        reportGenerated.value = true;
        generatingReport.value = false;
        ElMessage.success('分析报告生成完成');
      }, 3000);
    };

    const viewReport = () => {
      reportDialogVisible.value = true;
    };

    const downloadReport = () => {
      const blob = new Blob([reportContent.value], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'report.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElMessage.success('报告已下载');
    };

    const downloadLog = () => {
      const blob = new Blob([log.value], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'logfile.log';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElMessage.success('日志已下载');
    };

    onMounted(() => {
      fetchTestCases();
    });

    return {
      api,
      apiOptions,
      selectedTests,
      testCases,
      loadingTestCases,  // 控制加载状态
      log,
      loading,
      generatingReport,
      reportGenerated,
      reportDialogVisible,
      reportContent,
      elapsedTime,
      startCheck,
      stopCheck,
      generateReport,
      viewReport,
      downloadReport,
      downloadLog,
      querySuggestions,
      handleSelect,
      formattedElapsedTime,
    };
  },
};
</script>

<style scoped>
.container {
  display: flex;
  gap: 20px;
}
.left-panel {
  flex: 1; /* Full width since we removed right-panel */
}
.config-card,
.result-card {
  background-color: #16171A;
  border-radius: 8px;
}
.form-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.form-item label {
  margin-right: 10px;
  width: 100px; /* Set a fixed width for labels */
}
.buttons {
  margin: 10px 0;
}
.result-buttons {
  display: flex;
  justify-content: flex-start; /* Left align buttons */
  gap: 10px; /* Add space between buttons */
}
.time-display {
  margin-top: 10px;
}
.log-output {
  margin-top: 20px;
  border: 1px solid #444;
  padding: 10px;
  background-color: #1f1f1f;
  white-space: pre-wrap; /* Ensure proper line breaks in logs */
  height: 200px; /* Set fixed height */
  overflow-y: auto; /* Allow scrolling */
}
.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.card-header {
  margin-bottom: 10px; /* 调整标题和内容之间的间距 */
}

</style>
