<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen bg-gradient-to-b from-gray-900 to-black flex items-center justify-center relative overflow-hidden">

    <!-- 主要内容容器 -->
    <div class="w-full max-w-md z-10">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-[#4ED9F5] mb-2">
          <i class="fas fa-shield-alt mr-2"></i>安全卫士
        </h1>
        <p class="text-gray-400">大模型赋能的车载系统漏洞检测平台</p>
      </div>

      <!-- 切换标签 -->
      <div class="flex justify-center mb-6">
        <button 
          @click="activeTab = 'login'" 
          :class="['px-4 py-2 rounded-tl-lg rounded-bl-lg transition-all duration-300', 
                   activeTab === 'login' ? 'bg-[#4ED9F5] text-white' : 'bg-gray-800 text-gray-400']"
        >
          登录
        </button>
        <button 
          @click="activeTab = 'register'" 
          :class="['px-4 py-2 rounded-tr-lg rounded-br-lg transition-all duration-300', 
                   activeTab === 'register' ? 'bg-[#4ED9F5] text-white' : 'bg-gray-800 text-gray-400']"
        >
          注册
        </button>
      </div>

      <!-- 登录表单 -->
      <form v-if="activeTab === 'login'" @submit.prevent="handleSubmit" class="space-y-4">
        <div class="relative">
          <input 
            type="text" 
            placeholder="用户名" 
            v-model="loginForm.username" 
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:border-[#4ED9F5] transition-all duration-300 text-white"
            :class="{'shake': loginErrors.username}"
          >
          <i class="fas fa-user absolute right-3 top-3 text-gray-500"></i>
          <p v-if="loginErrors.username" class="text-red-400 text-sm mt-1">{{ loginErrors.username }}</p>
        </div>
        <div class="relative">
          <input 
            type="password" 
            placeholder="密码" 
            v-model="loginForm.password" 
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:border-[#4ED9F5] transition-all duration-300 text-white"
            :class="{'shake': loginErrors.password}"
          >
          <i class="fas fa-lock absolute right-3 top-3 text-gray-500"></i>
          <p v-if="loginErrors.password" class="text-red-400 text-sm mt-1">{{ loginErrors.password }}</p>
        </div>
        <button 
          type="submit" 
          class="w-full py-2 px-4 bg-[#4ED9F5] text-white rounded-md hover:bg-[#3bc8e6] transition-all duration-300 !rounded-button whitespace-nowrap"
        >
          登录
        </button>
      </form>

      <!-- 注册表单 -->
      <form v-else @submit.prevent="handleSubmit" class="space-y-4">
        <div class="relative">
          <input 
            type="text" 
            placeholder="用户名" 
            v-model="registerForm.username" 
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:border-[#4ED9F5] transition-all duration-300 text-white"
            :class="{'shake': registerErrors.username}"
          >
          <i class="fas fa-user absolute right-3 top-3 text-gray-500"></i>
          <p v-if="registerErrors.username" class="text-red-400 text-sm mt-1">{{ registerErrors.username }}</p>
        </div>
        <div class="relative">
          <input 
            type="email" 
            placeholder="电子邮箱" 
            v-model="registerForm.email" 
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:border-[#4ED9F5] transition-all duration-300 text-white"
            :class="{'shake': registerErrors.email}"
          >
          <i class="fas fa-envelope absolute right-3 top-3 text-gray-500"></i>
          <p v-if="registerErrors.email" class="text-red-400 text-sm mt-1">{{ registerErrors.email }}</p>
        </div>
        <div class="relative">
          <input 
            type="password" 
            placeholder="密码" 
            v-model="registerForm.password" 
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:border-[#4ED9F5] transition-all duration-300 text-white"
            :class="{'shake': registerErrors.password}"
          >
          <i class="fas fa-lock absolute right-3 top-3 text-gray-500"></i>
          <p v-if="registerErrors.password" class="text-red-400 text-sm mt-1">{{ registerErrors.password }}</p>
        </div>
        <button 
          type="submit" 
          class="w-full py-2 px-4 bg-[#4ED9F5] text-white rounded-md hover:bg-[#3bc8e6] transition-all duration-300 !rounded-button whitespace-nowrap"
        >
          注册
        </button>
      </form>

    </div>


  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { onMounted } from 'vue';

import { Engine } from "tsparticles-engine";
import { loadFull } from "tsparticles";

const activeTab = ref('login');
const isLoading = ref(false);

const loginForm = reactive({
  username: '',
  password: ''
});

const registerForm = reactive({
  username: '',
  email: '',
  password: ''
});

const loginErrors = reactive({
  username: '',
  password: ''
});

const registerErrors = reactive({
  username: '',
  email: '',
  password: ''
});

const handleSubmit = () => {
  isLoading.value = true;
  if (activeTab.value === 'login') {
    // 登录逻辑
    if (!loginForm.username) {
      loginErrors.username = '请输入用户名';
    } else {
      loginErrors.username = '';
    }
    if (!loginForm.password) {
      loginErrors.password = '请输入密码';
    } else {
      loginErrors.password = '';
    }
  } else {
    // 注册逻辑
    if (!registerForm.username) {
      registerErrors.username = '请输入用户名';
    } else {
      registerErrors.username = '';
    }
    if (!registerForm.email) {
      registerErrors.email = '请输入电子邮箱';
    } else if (!/\S+@\S+\.\S+/.test(registerForm.email)) {
      registerErrors.email = '请输入有效的电子邮箱';
    } else {
      registerErrors.email = '';
    }
    if (!registerForm.password) {
      registerErrors.password = '请输入密码';
    } else if (registerForm.password.length < 6) {
      registerErrors.password = '密码长度至少为6位';
    } else {
      registerErrors.password = '';
    }
  }

  // 模拟API调用
  setTimeout(() => {
    isLoading.value = false;
    // 这里可以添加实际的登录/注册逻辑
  }, 2000);
};

onMounted(() => {
  // @ts-ignore
  particlesJS('particles-js', {
    particles: {
      number: { value: 80, density: { enable: true, value_area: 800 } },
      color: { value: '#4ED9F5' },
      shape: { type: 'circle' },
      opacity: { value: 0.5, random: false },
      size: { value: 3, random: true },
      line_linked: { enable: true, distance: 150, color: '#4ED9F5', opacity: 0.4, width: 1 },
      move: { enable: true, speed: 2, direction: 'none', random: false, straight: false, out_mode: 'out', bounce: false }
    },
    interactivity: {
      detect_on: 'canvas',
      events: { onhover: { enable: true, mode: 'repulse' }, onclick: { enable: true, mode: 'push' }, resize: true },
      modes: { repulse: { distance: 100, duration: 0.4 }, push: { particles_nb: 4 } }
    },
    retina_detect: true
  });
});
</script>

<style scoped>
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.shake {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

.social-btn {
  @apply w-10 h-10 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:border-[#4ED9F5] hover:text-[#4ED9F5] transition-all duration-300 transform hover:scale-110;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>

