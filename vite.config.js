import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
    plugins: [vue()],
    resolve:{
      alias:{
        '@': path.resolve(__dirname,'src')
      }
    },
    // css: {
    //   loaderOptions:{
    //         sass: {
    //             additionalData: `@import "@/assets/styles/element-custom.scss";`
    //         }
    //     }
    // }
});

